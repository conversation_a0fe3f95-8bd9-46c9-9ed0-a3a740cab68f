﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="about" xml:space="preserve">
    <value>Über</value>
  </data>
  <data name="dropdown_filter_groupKey_fieldName" xml:space="preserve">
    <value>Schlüsselgruppenfilter</value>
  </data>
  <data name="search_by_key_fieldName" xml:space="preserve">
    <value> Suche nach Schlüsseln</value>
  </data>
  <data name="loading_spinner_span" xml:space="preserve">
    <value>Laden...</value>
  </data>
  <data name="show_mandatory_fields_label" xml:space="preserve">
    <value>Pflichtfelder anzeigen</value>
  </data>
  <data name="show_modified_fields_label" xml:space="preserve">
    <value>Geänderte Felder anzeigen</value>
  </data>
  <data name="restore_last_file_button_btnSecondary" xml:space="preserve">
    <value>Letzte Datei wiederherstellen</value>
  </data>
  <data name="change_structure_button_btnDanger" xml:space="preserve">
    <value>Struktur wechseln</value>
  </data>
  <data name="save_button" xml:space="preserve">
    <value>Speichern</value>
  </data>
  <data name="confirm_switch_to_new_structure" xml:space="preserve">
    <value>Sind Sie sicher, dass Sie die Struktur ändern möchten?</value>
  </data>
  <data name="changeStructure_button_yes" xml:space="preserve">
    <value>STRUKTUR ÄNDERN</value>
  </data>
  <data name="cancel_button_no" xml:space="preserve">
    <value>ABBRECHEN</value>
  </data>
  <data name="restore_action" xml:space="preserve">
    <value>Wiederherstellen</value>
  </data>
  <data name="label_create_section" xml:space="preserve">
    <value>Eine Sektion erstellen</value>
  </data>
  <data name="confirm_restore_last_saved_file" xml:space="preserve">
    <value>Sind Sie sicher, dass Sie die letzte gespeicherte Datei wiederherstellen möchten?</value>
  </data>
  <data name="search_key_input_placeholder" xml:space="preserve">
    <value>Geben Sie einen Schlüssel ein...</value>
  </data>
  <data name="dropdown_choose_section" xml:space="preserve">
    <value>Wählen Sie einen Abschnitt...</value>
  </data>
  <data name="choose_structure" xml:space="preserve">
    <value>Struktur wählen</value>
  </data>
  <data name="confirm_dialog_yes_button_text" xml:space="preserve">
    <value>Ich habe verstanden</value>
  </data>
  <data name="confirm_dialog_message_time_expired" xml:space="preserve">
    <value>Die Zeit ist abgelaufen. Wir leiten Sie zur Startseite weiter</value>
  </data>
  <data name="toast_message_title_modifications_in_progress" xml:space="preserve">
    <value>Änderungen im Gange</value>
  </data>
  <data name="toast_message_no_changes_in_form" xml:space="preserve">
    <value>Keine Änderungen in diesem Formular!</value>
  </data>
  <data name="toast_message_title_form_validation" xml:space="preserve">
    <value>Formularvalidierung</value>
  </data>
  <data name="toast_message_empty_required_fields" xml:space="preserve">
    <value>Es gibt noch leere Pflichtfelder</value>
  </data>
  <data name="confirm_save_changes_message" xml:space="preserve">
    <value>Sind Sie sicher, dass Sie die Änderungen speichern möchten?</value>
  </data>
  <data name="popup_save_success_message" xml:space="preserve">
    <value>Änderungen erfolgreich gespeichert, Gut gemacht!</value>
  </data>
  <data name="popup_save_success_button" xml:space="preserve">
    <value>Zurück zur Startseite</value>
  </data>
  <data name="file_in_use_message" xml:space="preserve">
    <value>Die Datei wird von {0} auf Struktur Nr. {1}  verwendet, bitte wechseln Sie die Struktur !</value>
  </data>
  <data name="create-new-structure" xml:space="preserve">
    <value>Neue Struktur erstellen</value>
  </data>
  <data name="config-ini" xml:space="preserve">
    <value>Config.ini-Datei verwalten</value>
  </data>
  <data name="appsettings-plateforms" xml:space="preserve">
    <value>App-Einstellungen Plattformen</value>
  </data>
  <data name="partners" xml:space="preserve">
    <value>Partnerverwaltung</value>
  </data>
  <data name="translations-terms" xml:space="preserve">
    <value>Übersetzungsverwaltung</value>
  </data>
  <data name="button_edit" xml:space="preserve">
    <value>Bearbeiten</value>
  </data>
  <data name="button_delete" xml:space="preserve">
    <value>Löschen</value>
  </data>
  <data name="select_all_sections" xml:space="preserve">
    <value>Alle Abschnitte</value>
  </data>
  <data name="select_view_empty_fields_language" xml:space="preserve">
    <value>Leere Felder der Sprache anzeigen</value>
  </data>
  <data name="button_filter" xml:space="preserve">
    <value>Filtern</value>
  </data>
  <data name="icon_search" xml:space="preserve">
    <value>Suchen</value>
  </data>
  <data name="button_new_translation" xml:space="preserve">
    <value>Neue Übersetzung</value>
  </data>
  <data name="label_translation_count" xml:space="preserve">
    <value>{0} Übersetzung(en)</value>
  </data>
  <data name="dialog_delete_message" xml:space="preserve">
    <value>Sind Sie sicher, dass Sie die Übersetzung {0} löschen möchten?</value>
  </data>
  <data name="button_cancel" xml:space="preserve">
    <value>Abbrechen</value>
  </data>
  <data name="translations_title" xml:space="preserve">
    <value>Übersetzungen</value>
  </data>
  <data name="new_partner" xml:space="preserve">
    <value>Neuer Partner</value>
  </data>
  <data name="search_button" xml:space="preserve">
    <value>Suchen</value>
  </data>
  <data name="confirm_delete_partner" xml:space="preserve">
    <value>Sind Sie sicher, dass Sie diesen Partner &lt;strong&gt;{0}&lt;/strong&gt; löschen möchten?</value>
  </data>
  <data name="confirm_button" xml:space="preserve">
    <value>Bestätigen</value>
  </data>
  <data name="choose_partner" xml:space="preserve">
    <value>Wählen Sie einen Partner</value>
  </data>
  <data name="new_secret_key" xml:space="preserve">
    <value>Neuer Geheimschlüssel</value>
  </data>
  <data name="button_validate" xml:space="preserve">
    <value>Bestätigen</value>
  </data>
  <data name="title_parameters" xml:space="preserve">
    <value>Parameter</value>
  </data>
  <data name="label_name_partener" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="partener_secret_key" xml:space="preserve">
    <value>Geheimer Schlüssel</value>
  </data>
  <data name="label_partener_structure" xml:space="preserve">
    <value>Strukturen</value>
  </data>
  <data name="label_partener_roles" xml:space="preserve">
    <value>Rollen</value>
  </data>
  <data name="title_edit_section" xml:space="preserve">
    <value>Abschnitt bearbeiten</value>
  </data>
  <data name="label_key_section" xml:space="preserve">
    <value>Abschnittsschlüssel</value>
  </data>
  <data name="label_description_section" xml:space="preserve">
    <value>Beschreibung</value>
  </data>
  <data name="h1_sections" xml:space="preserve">
    <value> Abschnitte</value>
  </data>
  <data name="search_by_keyword" xml:space="preserve">
    <value>Nach Schlüsselwort suchen</value>
  </data>
  <data name="new_section" xml:space="preserve">
    <value>Neue Sektion</value>
  </data>
  <data name="confirmation_delete_section" xml:space="preserve">
    <value>Sind Sie sicher, dass Sie die Sektion</value>
  </data>
  <data name="new_variable" xml:space="preserve">
    <value>Neue Variable</value>
  </data>
  <data name="label_variable_name" xml:space="preserve">
    <value>Variablenname</value>
  </data>
  <data name="label_description_variable" xml:space="preserve">
    <value>Beschreibung</value>
  </data>
  <data name="self_closing_tag" xml:space="preserve">
    <value>Selbstschließendes Tag</value>
  </data>
  <data name="edit_variable" xml:space="preserve">
    <value>Eine Variable bearbeiten</value>
  </data>
  <data name="h1_variables" xml:space="preserve">
    <value>Variablen</value>
  </data>
  <data name="confirm_delete_variable" xml:space="preserve">
    <value>Sind Sie sicher, dass Sie diese Variable löschen möchten?</value>
  </data>
  <data name="remaining_time" xml:space="preserve">
    <value>Verbleibende Zeit</value>
  </data>
  <data name="new_translation" xml:space="preserve">
    <value>Neue Übersetzung</value>
  </data>
  <data name="section_name" xml:space="preserve">
    <value>Abschnittsname</value>
  </data>
  <data name="select_section" xml:space="preserve">
    <value>Wählen Sie einen Abschnitt</value>
  </data>
  <data name="translation_key" xml:space="preserve">
    <value>Übersetzungsschlüssel</value>
  </data>
  <data name="parent_translation_key" xml:space="preserve">
    <value>Schlüssel der übergeordneten Übersetzung</value>
  </data>
  <data name="select_parent_key" xml:space="preserve">
    <value>Wählen Sie einen übergeordneten Schlüssel</value>
  </data>
  <data name="linked_to_existing_key" xml:space="preserve">
    <value>Verknüpft mit einem vorhandenen Schlüssel</value>
  </data>
  <data name="visible_only_for_admin_status" xml:space="preserve">
    <value>Nur für den Admin-Status sichtbar</value>
  </data>
  <data name="edit_translation" xml:space="preserve">
    <value>Eine Übersetzung bearbeiten</value>
  </data>
  <data name="toast_message_title_deletion_success" xml:space="preserve">
    <value>Löschung erfolgreich</value>
  </data>
  <data name="toast_message_deletion_successful" xml:space="preserve">
    <value>Die Löschung wurde erfolgreich abgeschlossen.</value>
  </data>
  <data name="error_partner_exists" xml:space="preserve">
    <value>Der Partner '{0}' existiert bereits.</value>
  </data>
  <data name="select_language" xml:space="preserve">
    <value>Wählen Sie eine Sprache aus</value>
  </data>
  <data name="label_translation_indiv_platform" xml:space="preserve">
    <value>Verwalten Sie Übersetzungen der INDIV-Plattform</value>
  </data>
  <data name="label_translation_customer_platform" xml:space="preserve">
    <value>Verwalten Sie die Übersetzungen der KUNDEN-Plattform</value>
  </data>
  <data name="label_translation_abo_platform" xml:space="preserve">
    <value>Verwalten Sie die Übersetzungen der KUNDEN-Plattform</value>
  </data>
  <data name="button_translations_indiv_platform" xml:space="preserve">
    <value>Übersetzungen der INDIV-Plattform</value>
  </data>
  <data name="button_translations_customer_platform" xml:space="preserve">
    <value>Übersetzungen des Raumkunden</value>
  </data>
  <data name="button_translations_abo_platform" xml:space="preserve">
    <value>Übersetzungen der Abonnementplattform</value>
  </data>
  <data name="error_exist_partener_title" xml:space="preserve">
    <value>Fehler</value>
  </data>
  <data name="delete_error_title" xml:space="preserve">
    <value>Löschfehler</value>
  </data>
  <data name="delete_errorm_message" xml:space="preserve">
    <value>Beim Löschen des Übersetzungsschlüssels {0} ist ein Fehler aufgetreten.</value>
  </data>
  <data name="delete_success_title" xml:space="preserve">
    <value>Löschung erfolgreich</value>
  </data>
  <data name="delete_success_message" xml:space="preserve">
    <value>Der Übersetzungsschlüssel {0} wurde erfolgreich gelöscht.</value>
  </data>
  <data name="partners_count" xml:space="preserve">
    <value>Partner</value>
  </data>
  <data name="no_partners_associated" xml:space="preserve">
    <value>Keine zugeordneten Partner</value>
  </data>
  <data name="select_structure" xml:space="preserve">
    <value>Struktur auswählen</value>
  </data>
  <data name="select_structure_placeholder" xml:space="preserve">
    <value>-- Struktur auswählen --</value>
  </data>
  <data name="search_partners_in_structure" xml:space="preserve">
    <value>Partner in dieser Struktur suchen</value>
  </data>
  <data name="search_partner_placeholder" xml:space="preserve">
    <value>Partnername eingeben...</value>
  </data>
  <data name="partners_of_structure" xml:space="preserve">
    <value>Partner von</value>
  </data>
  <data name="partners_found" xml:space="preserve">
    <value>Partner gefunden</value>
  </data>
  <data name="linked_structures" xml:space="preserve">
    <value>Verknüpfte Struktur(en)</value>
  </data>
  <data name="no_partners_found_search" xml:space="preserve">
    <value>Keine Partner gefunden, die Ihrer Suche entsprechen</value>
  </data>
  <data name="no_partners_in_structure" xml:space="preserve">
    <value>Diese Struktur hat keine zugeordneten Partner</value>
  </data>
  <data name="select_structure_to_view_partners" xml:space="preserve">
    <value>Wählen Sie oben eine Struktur aus, um ihre Partner anzuzeigen</value>
  </data>
  <data name="toast_save_success_message" xml:space="preserve">
    <value>Registrierung erfolgreich</value>
  </data>
  <data name="toast_message_title_success_edit" xml:space="preserve">
    <value>Änderung erfolgreich</value>
  </data>
  <data name="toast_message_success_edit_partner_saved" xml:space="preserve">
    <value>Die Änderungen des Partners wurden erfolgreich gespeichert.</value>
  </data>
  <data name="message_champ_obligatoire_xml" xml:space="preserve">
    <value>Dieses Feld ist erforderlich</value>
  </data>
  <data name="variables_comment_montant_gratuit_xml" xml:space="preserve">
    <value>Drei Fälle für den Betrag: 0:0, 1: kostenlos, 2: nicht enthalten</value>
  </data>
  <data name="variables_comment_xml" xml:space="preserve">
    <value>Werte von 5 bis 7</value>
  </data>
  <data name="variables_comment_type_alpha_xml" xml:space="preserve">
    <value>Alphanumerisch</value>
  </data>
  <data name="paiement_comment_operateur_creation_commande_xml" xml:space="preserve">
    <value>Operator für die Erstellung der Bestellung verwendet</value>
  </data>
  <data name="paiement_comment_web_operateur_id_xml" xml:space="preserve">
    <value>Web-Operator-ID</value>
  </data>
  <data name="paiement_comment_web_post_id_xml" xml:space="preserve">
    <value>Web-Post-ID</value>
  </data>
  <data name="paiement_comment_currency_xml" xml:space="preserve">
    <value>Währung, die in der Transaktion verwendet wird</value>
  </data>
  <data name="paiement_comment_activation_paiement_multiple_xml" xml:space="preserve">
    <value>Aktivierung der Mehrfachzahlung</value>
  </data>
  <data name="paiement_comment_capture_day_xml" xml:space="preserve">
    <value>Zahlungserfassungsdatum</value>
  </data>
  <data name="paiement_comment_periode_xml" xml:space="preserve">
    <value>Zahlungszeitraum</value>
  </data>
  <data name="paiement_comment_montant_min_nb_payment_xml" xml:space="preserve">
    <value>Mindestbetrag für die Anzahl der Zahlungen</value>
  </data>
  <data name="paiement_comment_filieren_payment_xml" xml:space="preserve">
    <value>Zahlungsweg</value>
  </data>
  <data name="paiement_comment_url_retour_err_xml" xml:space="preserve">
    <value>Rückgabe-URL bei Fehler</value>
  </data>
  <data name="paiement_comment_url_retour_vente_xml" xml:space="preserve">
    <value>Rückgabe-URL nach einem Verkauf</value>
  </data>
  <data name="paiement_comment_url_retour_err_vente_xml" xml:space="preserve">
    <value>Rückgabe-URL bei Fehler während des Verkaufs</value>
  </data>
  <data name="paiement_comment_url_retourannul_vente_xml" xml:space="preserve">
    <value>Rückgabe-URL für eine stornierte Verkaufstransaktio</value>
  </data>
  <data name="paiement_comment_url_retour_groupe_xml" xml:space="preserve">
    <value>Rückgabe-URL für eine Gruppe</value>
  </data>
  <data name="paiement_comment_url_retour_err_groupe_xml" xml:space="preserve">
    <value> URL de retour en cas d'erreur pour un groupe</value>
  </data>
  <data name="paiement_comment_url_retourannul_groupe_xml" xml:space="preserve">
    <value>Rückgabe-URL für eine stornierte Gruppe</value>
  </data>
  <data name="paiement_comment_nb_payment_xml" xml:space="preserve">
    <value>Anzahl der zulässigen Zahlungen</value>
  </data>
  <data name="paiement_comment_doe_edition_xml" xml:space="preserve">
    <value>Aktivierung der Dokumentenbearbeitung</value>
  </data>
  <data name="paiement_comment_activation_asynchrone_xml" xml:space="preserve">
    <value>Aktivierung der asynchronen Verarbeitung</value>
  </data>
  <data name="paiement_comment_not_asynchrone_pa_xml" xml:space="preserve">
    <value>Nicht asynchron für das Käuferprofil</value>
  </data>
  <data name="paiement_comment_urls_site_xml" xml:space="preserve">
    <value>Website-URL für die Zahlung</value>
  </data>
  <data name="paiement_comment_version_xml" xml:space="preserve">
    <value>Version der Zahlungsanwendung</value>
  </data>
  <data name="paiement_comment_activation_template_mail_xml" xml:space="preserve">
    <value>Aktivierung der E-Mail-Vorlage</value>
  </data>
  <data name="paiement_comment_desactiver_envoi_piece_jointe_pdf_xml" xml:space="preserve">
    <value>Deaktivieren des Sendens von Anhängen (PDF)</value>
  </data>
  <data name="paiement_comment_id_marchand_spplus_xml" xml:space="preserve">
    <value>SP+ Händler-ID</value>
  </data>
  <data name="paiement_comment_url_retour_abo_xml" xml:space="preserve">
    <value>Rückgabe-URL für das Abonnement</value>
  </data>
  <data name="paiement_comment_url_retour_err_abo_xml" xml:space="preserve">
    <value> URL de retour en cas d'erreur pour l'abonnement</value>
  </data>
  <data name="paiement_comment_url_retourannul_abo_xml" xml:space="preserve">
    <value>Rückgabe-URL für die Stornierung des Abonnements</value>
  </data>
  <data name="param_langue_com_configini_xml" xml:space="preserve">
    <value>Sprachparameter für die Konfiguration</value>
  </data>
  <data name="param_comment_devise_code_xml" xml:space="preserve">
    <value>Währungscode</value>
  </data>
  <data name="param_comment_devise_iso_xml" xml:space="preserve">
    <value>ISO-Code der Währung</value>
  </data>
  <data name="param_comment_devise_before_xml" xml:space="preserve">
    <value>Position der Währung (vor oder nach dem Betrag)</value>
  </data>
  <data name="param_comment_devise_separator_xml" xml:space="preserve">
    <value>Währungsseparator</value>
  </data>
  <data name="param_comment_prestataire_paiement_xml" xml:space="preserve">
    <value>Zahlungsanbieter</value>
  </data>
  <data name="param_comment_bonscadeaux_xml" xml:space="preserve">
    <value>Verfügbare Geschenkgutscheine</value>
  </data>
  <data name="param_comment_bannername_xml" xml:space="preserve">
    <value>Bannername</value>
  </data>
  <data name="param_comment_extensionbanner_xml" xml:space="preserve">
    <value>Banner-Erweiterung (wir nehmen die erste gefundene)</value>
  </data>
  <data name="param_comment_insurance_xml" xml:space="preserve">
    <value>Versicherung verwalten, ja=1</value>
  </data>
  <data name="param_comment_minutestogotopaiement_xml" xml:space="preserve">
    <value>Zeit vor der Zahlung (in Minuten</value>
  </data>
  <data name="param_comment_fraisdossiermontantgratuit_xml" xml:space="preserve">
    <value>Aktengebühren für kostenlosen Betrag</value>
  </data>
  <data name="param_comment_groupe_manif_exclude_widget_xml" xml:space="preserve">
    <value>Ausschluss der Widget-Gruppe für Manifestationen</value>
  </data>
  <data name="param_comment_multilangue_xml" xml:space="preserve">
    <value>Aktivierung von Mehrsprachigkeit</value>
  </data>
  <data name="param_comment_langue_en_xml" xml:space="preserve">
    <value>Englischsprache</value>
  </data>
  <data name="param_comment_langue_it_xml" xml:space="preserve">
    <value>Italienische Sprache</value>
  </data>
  <data name="param_comment_langue_sp_xml" xml:space="preserve">
    <value>Spanische Sprache</value>
  </data>
  <data name="param_comment_langue_de_xml" xml:space="preserve">
    <value>Deutsche Sprache</value>
  </data>
  <data name="param_comment_showonlyonedate_xml" xml:space="preserve">
    <value>Zeige nur ein Datum</value>
  </data>
  <data name="param_comment_usecustomerarea_xml" xml:space="preserve">
    <value>Kundenzone verwenden</value>
  </data>
  <data name="facture_mode_comment_facturemode_id_xml" xml:space="preserve">
    <value>Zahlungsmethode für Rechnung</value>
  </data>
  <data name="param_comment_facturemode_seuildeblocage_xml" xml:space="preserve">
    <value>Sperrschwelle</value>
  </data>
  <data name="comment_smtpclient_ip_xml" xml:space="preserve">
    <value>Mailserver-IP</value>
  </data>
  <data name="comment_navigation_accesfchoixseance_xml " xml:space="preserve">
    <value>Veraltete Schlüsselkategorie</value>
  </data>
  <data name="comment_navigation_panier_xml" xml:space="preserve">
    <value>Veraltete Schlüsselkategorie</value>
  </data>
  <data name="comment_priseplacessurplan_showunavailableseats_xml" xml:space="preserve">
    <value>Zeige nicht verfügbare Sitze</value>
  </data>
  <data name="comment_priseplacessurplan_startimagesfauteuilsx_xml" xml:space="preserve">
    <value>Minimale Stuhlgröße X</value>
  </data>
  <data name="comment_priseplacessurplan_startimagesfauteuilsy_xml" xml:space="preserve">
    <value>Minimale Stuhlgröße Y</value>
  </data>
  <data name="comment_marker_printathome_xml" xml:space="preserve">
    <value>Zeigt einen bestimmten Text, wenn Print@Home gewählt wird</value>
  </data>
  <data name="comment_marker_controlprice_xml" xml:space="preserve">
    <value>ID der Tarife, die einen spezifischen Text anzeigen</value>
  </data>
  <data name="comment_email_senderadresse_xml" xml:space="preserve">
    <value>Absenderadresse für Bestellbestätigungs-E-Mail</value>
  </data>
  <data name="comment_email_replyadresse_xml" xml:space="preserve">
    <value>Antwortadresse</value>
  </data>
  <data name="comment_email_copyadresse_xml" xml:space="preserve">
    <value>Kopie-Adresse für Bestellbestätigungs-E-Mail</value>
  </data>
  <data name="comment_email_blindcopyadresse_xml" xml:space="preserve">
    <value>Blindkopie-Adresse</value>
  </data>
  <data name="emailerror_comment_senderadresse_xml" xml:space="preserve">
    <value>Absenderadresse</value>
  </data>
  <data name="emailerror_comment_replyadresse_xml" xml:space="preserve">
    <value>Antwortadresse</value>
  </data>
  <data name="emailerror_comment_copyadresse_xml" xml:space="preserve">
    <value>Kopie-Adresse</value>
  </data>
  <data name="emailerror_comment_blindcopyadresse_xml" xml:space="preserve">
    <value>Blindkopie-Adresse</value>
  </data>
  <data name="emailerror_comment_inscriptionsenderadresse_xml" xml:space="preserve">
    <value>Absender für Anmelde-E-Mail</value>
  </data>
  <data name="emailerror_comment_passwordcopyadresse_xml" xml:space="preserve">
    <value>Absender für Passwort-vergessen-E-Mail</value>
  </data>
  <data name="kiosq_comment_weboperatorid_xml" xml:space="preserve">
    <value>Betreiber, der vom KIOSQ verwendet wird</value>
  </data>
  <data name="kiosq_comment_filiereid_xml" xml:space="preserve">
    <value>Bereichs-ID</value>
  </data>
  <data name="kiosq_comment_tarifidref_xml" xml:space="preserve">
    <value>Tarif-Referenz-ID</value>
  </data>
  <data name="kiosq_comment_cbmodeidid_xml" xml:space="preserve">
    <value>Kreditkartenmodus-ID</value>
  </data>
  <data name="kiosq_comment_postid_xml" xml:space="preserve">
    <value>Verkaufsstellen-ID</value>
  </data>
  <data name="prisepacessurplan_comment_ismultizones_xml" xml:space="preserve">
    <value> Multizonenverkauf</value>
  </data>
  <data name="createprofilabo_comment_webfiliereid_xml" xml:space="preserve">
    <value>Abonnement - Profil erstellen - Branche</value>
  </data>
  <data name="createprofil_comment_mailunicity_xml" xml:space="preserve">
    <value>Einzigartige E-Mail (0: Nein, 1: Ja, -1: Deaktivieren) für Kundenprofil</value>
  </data>
  <data name="createprofil_comment_webfiliereid_xml" xml:space="preserve">
    <value>Online-Verkauf - Profil erstellen - Branche</value>
  </data>
  <data name="createprofillink_comment_mailunicity_xml " xml:space="preserve">
    <value>E-Mail-Einzigartigkeit für Profil</value>
  </data>
  <data name="emailsupportingdocuments_comment_senderadresse_xml" xml:space="preserve">
    <value>Absenderadresse</value>
  </data>
  <data name="emailsupportingdocuments_comment_receiveradresse_xml" xml:space="preserve">
    <value>Empfängeradresse</value>
  </data>
  <data name="emailsupportingdocuments_comment_replyadresse_xml" xml:space="preserve">
    <value>Antwortadresse</value>
  </data>
  <data name="emailsupportingdocuments_comment_copyadresse_xml" xml:space="preserve">
    <value>Kopie-Adresse</value>
  </data>
  <data name="emailsupportingdocuments_comment_smtpclientip_xml" xml:space="preserve">
    <value>SMTP-Client-Server-IP</value>
  </data>
  <data name="supportingdocuments_comment_sizeattachments_xml" xml:space="preserve">
    <value>Maximale Upload-Größe erlaubt</value>
  </data>
  <data name="edcampaign_comment_presta_xml" xml:space="preserve">
    <value>Liste der E-Mail-Anbieter</value>
  </data>
  <data name="edcampaign_comment_eccmkey_xml" xml:space="preserve">
    <value>ECCM-Schlüssel für die Integration</value>
  </data>
  <data name="specifique_comment_generernumerofacture_xml" xml:space="preserve">
    <value>Automatische Generierung der Rechnungsnummern</value>
  </data>
  <data name="specifique_comment_flagdesplacesdanspaiement_xml" xml:space="preserve">
    <value>Indikator zur Verwaltung der Plätze während der Zahlung</value>
  </data>
  <data name="specifique_comment_seanceidnotset_xml" xml:space="preserve">
    <value>Sitzung-ID nicht definiert</value>
  </data>
  <data name="acomptes_comment_modespaiement_xml" xml:space="preserve">
    <value>Zahlungsmodus-ID für die Anzahlung</value>
  </data>
  <data name="abov2_comment_offreidhorsabo_xml" xml:space="preserve">
    <value>ID des Angebots außerhalb des Abos auf der Abo-Plattform</value>
  </data>
  <data name="paypalconnect_comment_username_xml" xml:space="preserve">
    <value>Benutzername für PayPal Connect</value>
  </data>
  <data name="paypalconnect_comment_password_xml" xml:space="preserve">
    <value>Passwort für PayPal Connect</value>
  </data>
  <data name="facebook_comment_applicationsecret_xml" xml:space="preserve">
    <value>Facebook-Anwendungsschlüssel</value>
  </data>
  <data name="facebookdev_comment_applicationsecret_xml" xml:space="preserve">
    <value>Facebook-Anwendungsschlüssel für Dev</value>
  </data>
  <data name="services_comment_opinion_order_xml" xml:space="preserve">
    <value>Anzeige der Meinungsreihenfolge auf der Zahlungsseite (1 zum Aktivieren, 0 zum Deaktivieren)</value>
  </data>
  <data name="coupefile_comment_futures_events_sessions_duration_xml" xml:space="preserve">
    <value>Dauer der Sitzungen für zukünftige Veranstaltungen</value>
  </data>
  <data name="coupefile_comment_message_commentaire_xml" xml:space="preserve">
    <value>Kommentar-Nachricht</value>
  </data>
  <data name="boutique_comment_delaicachehome_xml" xml:space="preserve">
    <value>Cache-Zeit für Shop-Startseite</value>
  </data>
  <data name="boutique_comment_delaicachefamille_xml" xml:space="preserve">
    <value>Cache-Zeit für Familienseite</value>
  </data>
  <data name="boutique_comment_delaicachesousfamille_xml " xml:space="preserve">
    <value>Cache-Zeit für Unterfamilienseite</value>
  </data>
  <data name="boutique_comment_delaicachedetail_xml" xml:space="preserve">
    <value>Cache-Zeit für Detailseite</value>
  </data>
  <data name="boutique_comment_delaicachemenu_xml" xml:space="preserve">
    <value>Cache-Zeit für das Menü</value>
  </data>
  <data name="revendeur_comment_ticketacapi_url_xml" xml:space="preserve">
    <value>URL für Echtzeit-Schnittstelle mit Ticketac-Händler</value>
  </data>
  <data name="revendeur_comment_ticketacapi_user_xml" xml:space="preserve">
    <value>Benutzer-ID für Echtzeit-Schnittstelle mit Ticketac</value>
  </data>
  <data name="revendeur_comment_ticketacapi_passw_xml" xml:space="preserve">
    <value>Passwort für Echtzeit-Schnittstelle mit Ticketac</value>
  </data>
  <data name="formule_comment_listeformuleid_xml" xml:space="preserve">
    <value>ID der verfügbaren Formelliste</value>
  </data>
  <data name="cbmodeidxfois_comment_idxfois_xml" xml:space="preserve">
    <value>Mehrfachzahlungsindex</value>
  </data>
  <data name="aexpta_comment_url_xml" xml:space="preserve">
    <value>URL für die Aexpta-Zahlungsplattform</value>
  </data>
  <data name="aexpta_comment_hmac_xml" xml:space="preserve">
    <value>HMAC-Schlüssel für sichere Transaktionen</value>
  </data>
  <data name="aexpta_comment_blowfish_xml" xml:space="preserve">
    <value>Blowfish-Schlüssel für die Verschlüsselung</value>
  </data>
  <data name="aexpta_comment_merchantid_xml" xml:space="preserve">
    <value>Händler-ID</value>
  </data>
  <data name="reabo_comment_formule_xml" xml:space="preserve">
    <value>Abo-Verlängerungsformel</value>
  </data>
  <data name="reabo_comment_dossieretatcheckabo_xml" xml:space="preserve">
    <value>Dateistatus für Abonnementprüfung</value>
  </data>
  <data name="reabo_comment_updatefansonstructure_xml" xml:space="preserve">
    <value>Fans in der Struktur aktualisieren</value>
  </data>
  <data name="reabo_comment_interdireinfocompupdatefans_xml" xml:space="preserve">
    <value>Zusatzinformationen für Fans untersagen</value>
  </data>
  <data name="atos64_comment_paiementenxfois_xml" xml:space="preserve">
    <value>Mehrfachzahlungsmodus</value>
  </data>
  <data name="indiv_comment_openprofilacheteurpopup_xml" xml:space="preserve">
    <value>Käuferprofil in Popup anzeigen</value>
  </data>
  <data name="indiv_comment_forcerloginlistemanifs_xml" xml:space="preserve">
    <value>Forcer la connexion pour la liste des manifestations</value>
  </data>
  <data name="indiv_comment_forcerloginpanier_xml" xml:space="preserve">
    <value>nmeldung für Warenkorb erzwingen</value>
  </data>
  <data name="emailkiosk_comment_senderadresse_xml" xml:space="preserve">
    <value>Absenderadresse</value>
  </data>
  <data name="emailkiosk_comment_replyadresse_xml" xml:space="preserve">
    <value>Antwortadresse</value>
  </data>
  <data name="emailkiosk_comment_copyadresse_xml" xml:space="preserve">
    <value>Kopieradresse</value>
  </data>
  <data name="emailkiosk_comment_trakingreceiver_xml" xml:space="preserve">
    <value>Tracking-Adresse</value>
  </data>
  <data name="emailkiosk_comment_overwriteuseradresse_xml" xml:space="preserve">
    <value>Benutzeradresse überschreiben</value>
  </data>
  <data name="emailinscription_comment_senderadresse_xml" xml:space="preserve">
    <value>Absender der Registrierungs-E-Mail</value>
  </data>
  <data name="emailinscription_comment_replyadresse_xml" xml:space="preserve">
    <value>Antwortadresse für Registrierungs-E-Mail</value>
  </data>
  <data name="emailinscription_comment_copyadresse_xml" xml:space="preserve">
    <value>Kopieradresse für Registrierungs-E-Mail</value>
  </data>
  <data name="emailseuilmini_comment_sendadresse_xml" xml:space="preserve">
    <value>Absender der E-Mail</value>
  </data>
  <data name="emailseuilmini_comment_senderadresse_xml" xml:space="preserve">
    <value>E-Mail-Absender</value>
  </data>
  <data name="emailseuilmini_comment_replyadresse_xml" xml:space="preserve">
    <value>Antwortadresse</value>
  </data>
  <data name="emailseuilmini_comment_copyadresse_xml" xml:space="preserve">
    <value>Kopieradresse</value>
  </data>
  <data name="revendeur_comment_duration_xml" xml:space="preserve">
    <value>Veranstaltungsdauer in Minuten für Wiederverkäufer (konfigurierbare Anfrage)</value>
  </data>
  <data name="revendeur_comment_message_commentaire_xml" xml:space="preserve">
    <value>Nachricht zum Download aus der Schnellspur</value>
  </data>
  <data name="smsefidem_comment_appid_xml" xml:space="preserve">
    <value>Anwendungs-ID</value>
  </data>
  <data name="smsefidem_comment_login_xml" xml:space="preserve">
    <value>Anmelde-ID</value>
  </data>
  <data name="smsefidem_comment_passw_xml" xml:space="preserve">
    <value>Passwort</value>
  </data>
  <data name="crmexterne_comment_type_xml" xml:space="preserve">
    <value>Typ</value>
  </data>
  <data name="crmexterne_comment_salt_xml" xml:space="preserve">
    <value>Salz</value>
  </data>
  <data name="crmexterne_comment_url_xml" xml:space="preserve">
    <value> URL</value>
  </data>
  <data name="unidy_comment_secret_xml" xml:space="preserve">
    <value> Geheimnis</value>
  </data>
  <data name="unidy_comment_client_xml" xml:space="preserve">
    <value>Kunde</value>
  </data>
  <data name="unidy_comment_redirecturl_xml" xml:space="preserve">
    <value>Weiterleitungs-URL</value>
  </data>
  <data name="unidy_comment_urltokenservice_xml" xml:space="preserve">
    <value>Token-Service-URL</value>
  </data>
  <data name="unidy_comment_urluinfo_xml" xml:space="preserve">
    <value>Benutzer-Info-UR</value>
  </data>
  <data name="virement_comment_hourslimitation_xml" xml:space="preserve">
    <value>Stundenbegrenzung</value>
  </data>
  <data name="assurancearteo_comment_senderadresse_xml" xml:space="preserve">
    <value>Absender der E-Mail</value>
  </data>
  <data name="assurancearteo_comment_replyadresse_xml" xml:space="preserve">
    <value>Antwortadresse</value>
  </data>
  <data name="assurancearteo_comment_copyadresse_xml" xml:space="preserve">
    <value> Kopieradresse</value>
  </data>
  <data name="assurancearteo_comment_smtpclientip_xml" xml:space="preserve">
    <value>SMTP-Client-IP-Adresse</value>
  </data>
  <data name="pelecard_comment_currency_xml" xml:space="preserve">
    <value>Währung</value>
  </data>
  <data name="pelecard_comment_login_xml" xml:space="preserve">
    <value>Anmelde-ID</value>
  </data>
  <data name="pelecard_comment_terminalid_xml" xml:space="preserve">
    <value>Terminal-ID</value>
  </data>
  <data name="pelecard_comment_password_xml" xml:space="preserve">
    <value>Mot de passe </value>
  </data>
  <data name="pelecard_comment_actiontype_xml" xml:space="preserve">
    <value>Aktionstyp</value>
  </data>
  <data name="pelecard_comment_url_xml" xml:space="preserve">
    <value>URL</value>
  </data>
  <data name="pelecard_comment_acceptedlanguage_xml " xml:space="preserve">
    <value>Akzeptierte Sprache</value>
  </data>
  <data name="paypalapi_comment_currency_xml" xml:space="preserve">
    <value>Devise</value>
  </data>
  <data name="paypalapi_comment_accesstoken_xml" xml:space="preserve">
    <value>Zugriffstoken</value>
  </data>
  <data name="paypalapi_comment_mode_xml" xml:space="preserve">
    <value>Modus</value>
  </data>
  <data name="paramprintathome_comment_ticketeos_urlkombiticket_xml" xml:space="preserve">
    <value>KombiTicket-URL</value>
  </data>
  <data name="paramprintathome_comment_ticketeos_creduser_xml" xml:space="preserve">
    <value>Benutzer-Login</value>
  </data>
  <data name="paramprintathome_comment_ticketeos_credpassw_xml" xml:space="preserve">
    <value>Benutzer-Passwort</value>
  </data>
  <data name="paramprintathome_comment_ticketeos_token_xml" xml:space="preserve">
    <value>Token</value>
  </data>
  <data name="paramprintathome_comment_ticketeos_systemid_xml" xml:space="preserve">
    <value>System-ID</value>
  </data>
  <data name="paramprintathome_comment_ticketeos_organizerid_xml" xml:space="preserve">
    <value>Organisator-ID</value>
  </data>
  <data name="emailpassword_comment_senderadresse_xml" xml:space="preserve">
    <value>Absender der E-Mail</value>
  </data>
  <data name="emailpassword_comment_replyadresse_xml" xml:space="preserve">
    <value> Antwortadresse</value>
  </data>
  <data name="emailpassword_comment_copyadresse_xml" xml:space="preserve">
    <value>Kopieradresse</value>
  </data>
  <data name="contraintesventes_comment_restrictiontarifparfichier_xml" xml:space="preserve">
    <value>Preiskontrolle pro Datei</value>
  </data>
  <data name="customerarea_comment_version_xml" xml:space="preserve">
    <value>Kundenbereich-Version</value>
  </data>
  <data name="customerarea_comment_checkinfocomplogin_xml" xml:space="preserve">
    <value>InfoComp-Login</value>
  </data>
  <data name="customerarea_comment_listinfocompdisponible_xml" xml:space="preserve">
    <value>Liste verfügbarer Infocomp</value>
  </data>
  <data name="createdossierproduits_comment_filiereid_xml" xml:space="preserve">
    <value>Abonnement - Erstellen Profil - Sektor</value>
  </data>
  <data name="cbmodeid_comment_xfois_xml" xml:space="preserve">
    <value>Zahlungsmethoden-ID für Ratenzahlung</value>
  </data>
  <data name="cbmodeid_comment_sofortberweisungde_xml" xml:space="preserve">
    <value>Zahlungsmethoden-ID SOFORT</value>
  </data>
  <data name="cbmodeid_comment_id_xml" xml:space="preserve">
    <value>Zahlungsmethoden-ID CB</value>
  </data>
  <data name="cbmodeid_comment_mastercard_xml" xml:space="preserve">
    <value>Zahlungsmethoden-ID MasterCard</value>
  </data>
  <data name="cbmodeid_comment_visa_xml" xml:space="preserve">
    <value>9930002</value>
  </data>
  <data name="cbmodeid_comment_ecard_xml" xml:space="preserve">
    <value>Zahlungsmethoden-ID E_CARD</value>
  </data>
  <data name="cbmodeid_comment_paypal_xml" xml:space="preserve">
    <value>Zahlungsmethoden-ID PAYPAL</value>
  </data>
  <data name="cbmodeid_comment_cvco_xml" xml:space="preserve">
    <value>Zahlungsmethoden-ID CVCO</value>
  </data>
  <data name="cbmodeid_comment_multi_xml" xml:space="preserve">
    <value>Zahlungsmethoden-ID MULTI</value>
  </data>
  <data name="cbmodeid_comment_sofort_xml" xml:space="preserve">
    <value>Zahlungsmethoden-ID SOFORT</value>
  </data>
  <data name="cbmodeid_comment_pa15_sofortberweisungde_xml" xml:space="preserve">
    <value>Zahlungsmethoden-ID SOFORT</value>
  </data>
  <data name="cbmodeid_comment_na_xml" xml:space="preserve">
    <value>Zahlungsmethoden-ID Rechnung oder Kostenlos</value>
  </data>
  <data name="cbmodeid_comment_pa_cb_xml" xml:space="preserve">
    <value>Zahlungsmethoden-ID CB</value>
  </data>
  <data name="cbmodeid_comment_pa_paypal_xml" xml:space="preserve">
    <value>Zahlungsmethoden-ID PAYPAL</value>
  </data>
  <data name="cbmodeid_comment_pa_sofortberweisungde_xml" xml:space="preserve">
    <value>Zahlungsmethoden-ID SOFORT</value>
  </data>
  <data name="cbmodeid_comment_pa_visa_xml" xml:space="preserve">
    <value>Zahlungsmethoden-ID VISA</value>
  </data>
  <data name="toast_message_connection_error_title" xml:space="preserve">
    <value>Verbindungsfehler</value>
  </data>
  <data name="toast_message_connection_error_body" xml:space="preserve">
    <value>Verbindung fehlgeschlagen. Bitte wählen Sie eine andere Struktur.</value>
  </data>
  <data name="button_translations_terms" xml:space="preserve">
    <value>Widget-Übersetzung</value>
  </data>
  <data name="button_sections" xml:space="preserve">
    <value>Übersetzung der Abschnitte</value>
  </data>
  <data name="button_variables" xml:space="preserve">
    <value>Übersetzung der Variablen</value>
  </data>
  <data name="modal_title_edit_section" xml:space="preserve">
    <value>Sektion bearbeiten</value>
  </data>
  <data name="confirm_delete" xml:space="preserve">
    <value>Sind Sie sicher, dass Sie die Variable '{0}' löschen möchten?</value>
  </data>
  <data name="delete_success" xml:space="preserve">
    <value>Die Variable &lt;strong&gt;'{0}'&lt;/strong&gt; wurde erfolgreich gelöscht.</value>
  </data>
  <data name="delete_error" xml:space="preserve">
    <value>Fehler beim Löschen der Variablen &lt;strong&gt;'{0}': {1}&lt;/strong&gt;</value>
  </data>
  <data name="confirm_delete_translation_area" xml:space="preserve">
    <value>Sind Sie sicher, dass Sie die Variable '{0}' löschen möchten?</value>
  </data>
  <data name="edit_partenaire" xml:space="preserve">
    <value>Einen Partner bearbeiten</value>
  </data>
  <data name="added_sections" xml:space="preserve">
    <value>Abschnitte hinzufügen</value>
  </data>
  <data name="mandatory_sections" xml:space="preserve">
    <value>Verpflichtende Konfiguration</value>
  </data>
  <data name="advanced_configuration" xml:space="preserve">
    <value>Zusätzliche Abschnitte</value>
  </data>
  <data name="confirm_dialog_yes_button" xml:space="preserve">
    <value>Ja</value>
  </data>
  <data name="confirm_dialog_no_button" xml:space="preserve">
    <value>Nein</value>
  </data>
  <data name="time_warning_message" xml:space="preserve">
    <value>Die Datei wird in: {0} Minute(n) und {1} Sekunde(n) gelöscht. Möchten Sie fortfahren?</value>
  </data>
  <data name="time_warning_title" xml:space="preserve">
    <value>Zeitwarnung</value>
  </data>
  <data name="toast_message_title_file_deletion" xml:space="preserve">
    <value>Dateilöschung</value>
  </data>
  <data name="toast_message_file_deletion" xml:space="preserve">
    <value>Die temporäre Datei wurde gelöscht, da die Zeit abgelaufen ist.</value>
  </data>
  <data name="selected_variables" xml:space="preserve">
    <value>Selected Variables</value>
  </data>
  <data name="translation_choose_variables" xml:space="preserve">
    <value>Choose variables</value>
  </data>
  <data name="translation_variable_fully_selected" xml:space="preserve">
    <value>Fully Selected</value>
  </data>
  <data name="translation_variable_partially_selected" xml:space="preserve">
    <value>Partially Selected</value>
  </data>
  <data name="translation_variable_not_selected" xml:space="preserve">
    <value>Not Selected</value>
  </data>
  <data name="Field_Validation_SingleEmail" xml:space="preserve">
    <value>Das Feld darf nur eine E-Mail-Adresse enthalten</value>
  </data>
  <data name="Field_Validation_ReplyAddress_SingleEmail" xml:space="preserve">
    <value>Der REPLYADRESSE-Feld darf nur eine einzige E-Mail-Adresse enthalten</value>
  </data>
  <data name="Field_Validation_InvalidEmail" xml:space="preserve">
    <value>Ungültige E-Mail-Adresse</value>
  </data>
  <data name="Field_Validation_InvalidEmail_WithDetails" xml:space="preserve">
    <value>Ungültige E-Mail-Adresse: {0}</value>
  </data>
  <data name="Field_Validation_DuplicateEmail" xml:space="preserve">
    <value>Doppelte E-Mail-Adresse erkannt.</value>
  </data>
  <data name="Field_Validation_DuplicateEmail_WithDetails" xml:space="preserve">
    <value>Doppelte E-Mail-Adressen erkannt: {0}</value>
  </data>
  <data name="close_modal" xml:space="preserve">
    <value> Schließen</value>
  </data>
  <data name="toast_message_title_success" xml:space="preserve">
    <value>Erfolg</value>
  </data>
  <data name="toast_message_title_error" xml:space="preserve">
    <value>Fehler</value>
  </data>
  <data name="toast_message_form_submission_failed" xml:space="preserve">
    <value>Formularübermittlung fehlgeschlagen: {0}</value>
  </data>
  <data name="toast_message_form_validation_failed" xml:space="preserve">
    <value>Allemand : Das Formular enthält Validierungsfehler</value>
  </data>
  <data name="gestion-webtracing" xml:space="preserve">
    <value>WebTracing-Verwaltung</value>
  </data>
  <data name="username_nom_specifie_webtracing" xml:space="preserve">
    <value>Nicht angegeben</value>
  </data>
  <data name="toast_message_structure_failed" xml:space="preserve">
    <value>Strukturdaten konnten nicht geladen werden</value>
  </data>
  <data name="toast_message_title_delete_structure_success" xml:space="preserve">
    <value>Struktur erfolgreich gelöscht</value>
  </data>
  <data name="toast_message_delete_success" xml:space="preserve">
    <value>Die Struktur wurde erfolgreich gelöscht.</value>
  </data>
  <data name="toast_message_title_delete_structure_error" xml:space="preserve">
    <value>Fehler beim Löschen der Struktur</value>
  </data>
  <data name="toast_message_delete_structure_failed" xml:space="preserve">
    <value>Struktur konnte nicht gelöscht werden</value>
  </data>
  <data name="webtracing_list_title" xml:space="preserve">
    <value>Liste der Strukturen mit WebTracing-Verbindungen</value>
  </data>
  <data name="webtracing_no_structure_found" xml:space="preserve">
    <value>Keine Struktur oder Verbindung gefunden.</value>
  </data>
  <data name="webtracing_deleted" xml:space="preserve">
    <value>Gelöscht</value>
  </data>
  <data name="webtracing_not_deleted" xml:space="preserve">
    <value>Nein</value>
  </data>
  <data name="webtracing_unspecified" xml:space="preserve">
    <value>Nicht angegeben</value>
  </data>
  <data name="webtracing_column_name" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="webtracing_column_deleted" xml:space="preserve">
    <value>Gelöscht</value>
  </data>
  <data name="webtracing_column_deletion_date" xml:space="preserve">
    <value>Löschdatum</value>
  </data>
  <data name="webtracing_column_deleted_by" xml:space="preserve">
    <value>Gelöscht von</value>
  </data>
  <data name="webtracing_column_database" xml:space="preserve">
    <value>Datenbank</value>
  </data>
  <data name="webtracing_column_start_date" xml:space="preserve">
    <value>Startdatum</value>
  </data>
  <data name="webtracing_column_stop_date" xml:space="preserve">
    <value>Enddatum</value>
  </data>
  <data name="webtracing_column_actions" xml:space="preserve">
    <value>Aktionen</value>
  </data>
  <data name="webtracing_button_deleted" xml:space="preserve">
    <value>Gelöscht</value>
  </data>
  <data name="webtracing_button_delete" xml:space="preserve">
    <value>Zu löschen</value>
  </data>
  <data name="role-management" xml:space="preserve">
    <value>Rollenverwaltung</value>
  </data>
  <data name="modal_success_message" xml:space="preserve">
    <value>Gutscheine wurden erfolgreich generiert und in der Datenbank gespeichert.</value>
  </data>
  <data name="rolemanagement_create_role" xml:space="preserve">
    <value>Rolle erstellen</value>
  </data>
  <data name="new-module" xml:space="preserve">
    <value>Prefix</value>
  </data>
  <data name="rolemanagement_create_group" xml:space="preserve">
    <value>Gruppe erstellen</value>
  </data>
  <data name="role_create_new" xml:space="preserve">
    <value>Erstellen Sie eine neue Rolle</value>
  </data>
  <data name="rolemanagement_create_module" xml:space="preserve">
    <value>Modul erstellen</value>
  </data>
  <data name="rolemanagement_groupname" xml:space="preserve">
    <value>Gruppenname</value>
  </data>
  <data name="rolemanagement_groupid" xml:space="preserve">
    <value>Gruppen-ID</value>
  </data>
  <data name="rolemanagement_modulename" xml:space="preserve">
    <value>Modulname</value>
  </data>
  <data name="rolemanagement_modulecomment" xml:space="preserve">
    <value>Kommentar</value>
  </data>
  <data name="rolemanagement_rolename" xml:space="preserve">
    <value>Rollenname</value>
  </data>
  <data name="rolemanagement_canread" xml:space="preserve">
    <value>Kann Lesen</value>
  </data>
  <data name="rolemanagement_cancreate" xml:space="preserve">
    <value>Kann Erstellen</value>
  </data>
  <data name="rolemanagement_canupdate" xml:space="preserve">
    <value>Kann Aktualisieren</value>
  </data>
  <data name="rolemanagement_candelete" xml:space="preserve">
    <value>Kann Löschen</value>
  </data>
  <data name="rolemanagement_level" xml:space="preserve">
    <value>Stufe</value>
  </data>
  <data name="rolemanagement_title" xml:space="preserve">
    <value>Rollen- und Modulverwaltung</value>
  </data>
  <data name="rolemanagement_loading" xml:space="preserve">
    <value>Bitte warten Sie, während die Daten geladen werden...</value>
  </data>
  <data name="rolemanagement_module" xml:space="preserve">
    <value>Modul</value>
  </data>
  <data name="rolemanagement_role" xml:space="preserve">
    <value>Role</value>
  </data>
  <data name="rolemanagement_no_module" xml:space="preserve">
    <value>Keine Rolle</value>
  </data>
  <data name="rolemanagement_no_module_or_role" xml:space="preserve">
    <value>Kein Modul oder Rolle für {0}</value>
  </data>
  <data name="rolemanagement_assign_roles" xml:space="preserve">
    <value>Rollenvergabe</value>
  </data>
  <data name="rolemanagement_modules" xml:space="preserve">
    <value>Module </value>
  </data>
  <data name="rolemanagement_ad_groups" xml:space="preserve">
    <value>AD-Gruppen</value>
  </data>
  <data name="rolemanagement_roles" xml:space="preserve">
    <value>Rollen</value>
  </data>
  <data name="rolemanagement_choose_option" xml:space="preserve">
    <value>Option wählen</value>
  </data>
  <data name="rolemanagement_assign" xml:space="preserve">
    <value>Zuweisen</value>
  </data>
  <data name="error_create_group_title" xml:space="preserve">
    <value>Fehler beim Erstellen der Gruppe</value>
  </data>
  <data name="error_create_group_message" xml:space="preserve">
    <value>Gruppe {0} konnte nicht erstellt werden. Bitte versuchen Sie es erneut.</value>
  </data>
  <data name="Serror_create_module_title" xml:space="preserve">
    <value>Fehler beim Erstellen des Moduls</value>
  </data>
  <data name="error_create_module_message" xml:space="preserve">
    <value>Modul {0} konnte nicht erstellt werden. Bitte versuchen Sie es erneut.</value>
  </data>
  <data name="error_create_role_title" xml:space="preserve">
    <value>Fehler beim Erstellen der Rolle</value>
  </data>
  <data name="error_create_role_message" xml:space="preserve">
    <value>Rolle {0} konnte nicht erstellt werden. Bitte versuchen Sie es erneut.</value>
  </data>
  <data name="error_loading_data_title" xml:space="preserve">
    <value>Fehler beim Laden der Daten</value>
  </data>
  <data name="error_loading_data_message" xml:space="preserve">
    <value>Beim Laden der Daten ist ein Fehler aufgetreten: {0}</value>
  </data>
  <data name="success_roles_assigned_title" xml:space="preserve">
    <value>Rollen erfolgreich zugewiesen</value>
  </data>
  <data name="success_roles_assigned_message" xml:space="preserve">
    <value>Die Rollen wurden erfolgreich zugewiesen.</value>
  </data>
  <data name="error_assign_roles_title" xml:space="preserve">
    <value>Fehler bei der Zuweisung von Rollen</value>
  </data>
  <data name="error_assign_roles_message" xml:space="preserve">
    <value>Fehlgeschlagene Zuweisung der Rollen: {0}</value>
  </data>
  <data name="error_group_exists" xml:space="preserve">
    <value>Die Gruppe {0} existiert bereits.</value>
  </data>
  <data name="error_module_exists" xml:space="preserve">
    <value>Das Modul {0} existiert bereits.</value>
  </data>
  <data name="error_role_exists" xml:space="preserve">
    <value>Die Rolle {0} existiert bereits.</value>
  </data>
  <data name="choisi_structure" xml:space="preserve">
    <value>gewählt</value>
  </data>
  <data name="error_search_partners_title" xml:space="preserve">
    <value>Fehler beim Suchen von Partnern</value>
  </data>
  <data name="no_roles" xml:space="preserve">
    <value>Keine Rollen</value>
  </data>
  <data name="label_roles" xml:space="preserve">
    <value>Rollen</value>
  </data>
  <data name="label_structures" xml:space="preserve">
    <value>Strukturen</value>
  </data>
  <data name=" no_structures" xml:space="preserve">
    <value>Keine Strukturen</value>
  </data>
  <data name="no_partner_structures" xml:space="preserve">
    <value>Keine Strukturen</value>
  </data>
  <data name="Stradd_missing_keys" xml:space="preserve">
    <value>Fehlende Schlüssel hinzufügen</value>
  </data>
  <data name="toast_add_missing_keys_success" xml:space="preserve">
    <value>Fehlende Schlüssel wurden erfolgreich hinzugefügt"</value>
  </data>
  <data name="add_missing_keys_title" xml:space="preserve">
    <value>Fehlende Schlüssel hinzufügen</value>
  </data>
  <data name="add_missing_keys_description" xml:space="preserve">
    <value>Einige Schlüssel fehlen. Möchten Sie sie hinzufügen?</value>
  </data>
  <data name="for_all_languages" xml:space="preserve">
    <value>Für alle Sprachen</value>
  </data>
  <data name="for_this_language" xml:space="preserve">
    <value>Für diese Sprache</value>
  </data>
  <data name="add_missing_keys" xml:space="preserve">
    <value>Fehlende Schlüssel hinzufügen</value>
  </data>
  <data name="payment_platform_translation" xml:space="preserve">
    <value>Übersetzung der Zahlungsplattform</value>
  </data>
  <data name="copy_to_test" xml:space="preserve">
    <value>In TEST kopieren</value>
  </data>
  <data name="copy_to_prod" xml:space="preserve">
    <value>In PROD kopieren</value>
  </data>
  <data name="copy_to_prod_confirmation" xml:space="preserve">
    <value>Sind Sie sicher, dass Sie die Daten nach PROD kopieren möchten?</value>
  </data>
  <data name="copy_to_test_confirmation" xml:space="preserve">
    <value>Sind Sie sicher, dass Sie die Daten nach TEST kopieren möchten?</value>
  </data>
  <data name="copy_to_prod_title" xml:space="preserve">
    <value>In PROD kopieren</value>
  </data>
  <data name="copy_to_test_title" xml:space="preserve">
    <value>In TEST kopieren</value>
  </data>
  <data name="toast_copy_to_test_success" xml:space="preserve">
    <value>DEV nach TEST kopieren erfolgreich</value>
  </data>
  <data name="toast_copy_to_prod_success" xml:space="preserve">
    <value>DEV nach PROD kopieren erfolgreich</value>
  </data>
  <data name="admin_platform_translation" xml:space="preserve">
    <value>Übersetzung der Admin-Plattform</value>
  </data>
  <data name="cbmodeid_comment_pa_paybox_xml" xml:space="preserve">
    <value>Zahlung, zwei Konten für einen Anbieter</value>
  </data>
  <data name="cbmodeid_comment_pa_paybox_url_redirection_xml" xml:space="preserve">
    <value>Weiterleitungs-URL zum Zahlungsanbieter</value>
  </data>
  <data name="cbmodeid_comment_pa_paybox_code_devise_xml" xml:space="preserve">
    <value>Währungscode der Website, der an den Anbieter gesendet wird</value>
  </data>
  <data name="cbmodeid_comment_pa_paybox_identifiant_structure_xml" xml:space="preserve">
    <value>Struktur-Identifikator</value>
  </data>
  <data name="cbmodeid_comment_pa_paybox_code_site_xml" xml:space="preserve">
    <value>Site code (provided by PAYBOX</value>
  </data>
  <data name="cbmodeid_comment_pa_paybox_rang_site_xml" xml:space="preserve">
    <value>Site rank (provided by PAYBOX)</value>
  </data>
  <data name="cbmodeid_comment_pa_paybox2_xml" xml:space="preserve">
    <value>Zahlung, zwei Konten für einen Anbieter</value>
  </data>
  <data name="cbmodeid_comment_pa_paybox2_url_redirection_xml" xml:space="preserve">
    <value>Weiterleitungs-URL zum Zahlungsanbieter</value>
  </data>
  <data name="cbmodeid_comment_pa_paybox2_code_devise_xml" xml:space="preserve">
    <value>Währungscode der Website, der an den Anbieter gesendet wird</value>
  </data>
  <data name="cbmodeid_comment_pa_paybox2_code_site_xml" xml:space="preserve">
    <value>Site code (provided by PAYBOX)</value>
  </data>
  <data name="cbmodeid_comment_pa_paybox2_rang_site_xml" xml:space="preserve">
    <value>Site rank (provided by PAYBOX)</value>
  </data>
  <data name="cbmodeid_comment_pa_paybox2_identifiant_structure_xml" xml:space="preserve">
    <value>Struktur-Identifikator</value>
  </data>
  <data name="cbmodeid_comment_pa_payboxhmac1_xml" xml:space="preserve">
    <value>Zahlung mit HMAC, zwei Konten für einen Anbieter</value>
  </data>
  <data name="cbmodeid_comment_pa_payboxhmac1_url_redirection_xml" xml:space="preserve">
    <value>Weiterleitungs-URL zum Zahlungsanbieter</value>
  </data>
  <data name="cbmodeid_comment_pa_payboxhmac1_code_devise_xml" xml:space="preserve">
    <value>Währungscode der Website, der an den Anbieter gesendet wird</value>
  </data>
  <data name="cbmodeid_comment_pa_payboxhmac1_code_site_xml" xml:space="preserve">
    <value>Site code (provided by PAYBOXHMAC)</value>
  </data>
  <data name="cbmodeid_comment_pa_payboxhmac1_rang_site_xml" xml:space="preserve">
    <value>Site rank (provided by PAYBOXHMAC)</value>
  </data>
  <data name="cbmodeid_comment_pa_payboxhmac1_identifiant_structure_xml" xml:space="preserve">
    <value>Struktur-Identifikator</value>
  </data>
  <data name="cbmodeid_comment_pa_payboxhmac1_hmac_xml" xml:space="preserve">
    <value>HMAC-Schlüssel zur Sicherung der Transaktion</value>
  </data>
  <data name="cbmodeid_comment_pa_payboxhmac2_xml" xml:space="preserve">
    <value>Zahlung mit HMAC, zwei Konten für einen Anbieter</value>
  </data>
  <data name="cbmodeid_comment_pa_payboxhmac2_url_redirection_xml" xml:space="preserve">
    <value>Weiterleitungs-URL zum Zahlungsanbieter</value>
  </data>
  <data name="cbmodeid_comment_pa_payboxhmac2_url_redirection_old_xml" xml:space="preserve">
    <value>Alte Weiterleitungs-URL zum Zahlungsanbieter</value>
  </data>
  <data name="cbmodeid_comment_pa_payboxhmac2_code_devise_xml" xml:space="preserve">
    <value>Währungscode der Website, der an den Anbieter gesendet wird</value>
  </data>
  <data name="cbmodeid_comment_pa_payboxhmac2_code_site_xml" xml:space="preserve">
    <value>Site code (provided by PAYBOXHMAC)</value>
  </data>
  <data name="cbmodeid_comment_pa_payboxhmac2_rang_site_xml" xml:space="preserve">
    <value>Site rank (provided by PAYBOXHMAC)</value>
  </data>
  <data name="cbmodeid_comment_pa_payboxhmac2_identifiant_structure_xml" xml:space="preserve">
    <value>Struktur-Identifikator</value>
  </data>
  <data name="cbmodeid_comment_pa_payboxhmac2_url_acquit_xml" xml:space="preserve">
    <value>Bestätigungs-URL für die Transaktion</value>
  </data>
  <data name="cbmodeid_comment_pa_payboxhmac2_hmac_xml" xml:space="preserve">
    <value>HMAC-Schlüssel zur Sicherung der Transaktion</value>
  </data>
  <data name="cbmodeid_comment_pa_cybermuth1_xml" xml:space="preserve">
    <value>Zahlung über CYBERMUTH, spezifische Integration</value>
  </data>
  <data name="cbmodeid_comment_pa_cybermuth1_url_redirection_xml" xml:space="preserve">
    <value>Weiterleitungs-URL zum Zahlungsanbieter</value>
  </data>
  <data name="cbmodeid_comment_pa_cybermuth1_code_devise_xml" xml:space="preserve">
    <value>Währungscode der Website, der an den Anbieter gesendet wird</value>
  </data>
  <data name="cbmodeid_comment_pa_cybermuth1_identifiant_structure_xml" xml:space="preserve">
    <value>Struktur-Identifikator</value>
  </data>
  <data name="cbmodeid_comment_pa_cybermuth1_code_site_xml" xml:space="preserve">
    <value> Site code (provided by CYBERMUTH)</value>
  </data>
  <data name="cbmodeid_comment_pa_cybermuth1_version_xml" xml:space="preserve">
    <value>Version des verwendeten Protokolls</value>
  </data>
  <data name="cbmodeid_comment_pa_cybermuth1_key_xml" xml:space="preserve">
    <value>Sicherheitschlüssel für Transaktionen</value>
  </data>
  <data name="cbmodeid_comment_pa_cybermuth1_langage_xml" xml:space="preserve">
    <value>Sprache für die Transaktion</value>
  </data>
  <data name="cbmodeid_comment_pa_cybermuth1_hashalgo_xml" xml:space="preserve">
    <value>Hash-Algorithmus zur Sicherheit</value>
  </data>
  <data name="cbmodeid_comment_pa_linkapiaccuse_xml" xml:space="preserve">
    <value>Link, der verwendet wird, um die Kartenerfassung nach erfolgreichem Empfang anzufordern</value>
  </data>
  <data name="cbmodeid_comment_pa_apipassword_xml" xml:space="preserve">
    <value>Passwort, das mit LINKAPIACCUSE verwendet wird</value>
  </data>
  <data name="cbmodeid_comment_pa_apilogin_xml" xml:space="preserve">
    <value>Benutzer, der mit LINKAPIACCUSE verwendet wird</value>
  </data>
  <data name="cbmodeid_comment_pa_s10reference_xml" xml:space="preserve">
    <value>Geben Sie diesen Schlüssel ein, wenn der Kunde einer vereinfachten Migration bei Atos unterzogen wird: Geben Sie diesen Schlüssel nur ein, wenn der Vertrag des Kunden nicht mit 0210 beginnt</value>
  </data>
  <data name="cbmodeid_comment_pa_versionkey_xml" xml:space="preserve">
    <value>Versionsnummer des verwendeten Schlüssels (normalerweise = 1, siehe das Backoffice des Anbieters des Kunden)</value>
  </data>
  <data name="cbmodeid_comment_pa_key_xml" xml:space="preserve">
    <value>Zugriffsschlüssel (Backoffice des Anbieters des Kunden)</value>
  </data>
  <data name="cbmodeid_comment_pa_interfaceversion_xml" xml:space="preserve">
    <value>Zahlungsanbieter-Version</value>
  </data>
  <data name="cbmodeid_comment_pa_autoripuiscapture_xml" xml:space="preserve">
    <value>Wenn O, wird zuerst eine Autorisierung durchgeführt und dann die Erfassung nach der Validierung. In diesem Fall müssen die nächsten 2 Schlüssel vorhanden sein</value>
  </data>
  <data name="cbmodeid_comment_pa_password_xml" xml:space="preserve">
    <value>Live- oder TEST-Geheimschlüssel, der im Backoffice zu finden ist</value>
  </data>
  <data name="cbmodeid_comment_pa_autoripuiscapture_true_false_xml" xml:space="preserve">
    <value>Wenn 1, wird die Autorisierung durchgeführt, dann Erfassung nach der Validierung, andernfalls keine Autorisierung</value>
  </data>
  <data name="cbmodeid_comment_pa_smsdefaultregion_xml" xml:space="preserve">
    <value>Der Schlüssel SMSDEFAULTREGION entspricht dem Standard-Ländercode für SMS-Kampagnen</value>
  </data>
  <data name="cbmodeid_comment_pa_webhooksalt_xml" xml:space="preserve">
    <value>Schlüssel, der von Sharegroop bereitgestellt wird, um die Signatur während der Benachrichtigung zu generieren</value>
  </data>
  <data name="cbmodeid_comment_pa_rang_xml" xml:space="preserve">
    <value>Seitenrang</value>
  </data>
  <data name="modifier-temps-panier" xml:space="preserve">
    <value>Warenkorbzeit</value>
  </data>
  <data name="Modifier_Temps_Panier" xml:space="preserve">
    <value>Warenkorbzeit bearbeiten</value>
  </data>
  <data name="Temps_Expiration_Panier" xml:space="preserve">
    <value>Ablaufzeit des Warenkorbs</value>
  </data>
  <data name="Saisir_Temps_Expiration" xml:space="preserve">
    <value>Geben Sie die Zeit vor Ablauf des Warenkorbs ein (1-1430 Minuten).</value>
  </data>
  <data name="Delai_Deflag" xml:space="preserve">
    <value>Deflag-Zeit (automatisch)</value>
  </data>
  <data name="Delai_Deflag_Description" xml:space="preserve">
    <value>Deflag-Zeit = Ablaufzeit + 10 Minuten (automatisch berechnet).</value>
  </data>
  <data name="retour_liste_struture" xml:space="preserve">
    <value>Zurück</value>
  </data>
  <data name="config_value_exceedsDB_error" xml:space="preserve">
    <value>Der Wert(ConfigIni) darf nicht größer sein als der Wert der Datenbank.</value>
  </data>
  <data name="common_error" xml:space="preserve">
    <value>Fehler</value>
  </data>
  <data name="temps_panier_saved_successfully" xml:space="preserve">
    <value>Warenkorbzeit erfolgreich gespeichert</value>
  </data>
  <data name="temps_panier_save_error" xml:space="preserve">
    <value>Fehler beim Speichern der Warenkorbzeit: {0}</value>
  </data>
  <data name="delai_deflag_null_error" xml:space="preserve">
    <value>Detonationsverzögerung ist null</value>
  </data>
  <data name="panier_expiration_inferieure_deflag" xml:space="preserve">
    <value>Die Ablaufzeit ({0}) darf nicht kleiner als der Entflammbarkeitswert ({1}) sein.</value>
  </data>
  <data name="gestion-coupons-promo" xml:space="preserve">
    <value>Gutscheinverwaltung</value>
  </data>
  <data name="no_profil_message" xml:space="preserve">
    <value>Kein Käuferprofil verfügbar.</value>
  </data>
  <data name="back_button" xml:space="preserve">
    <value>Zurück</value>
  </data>
  <data name="general_title" xml:space="preserve">
    <value>Verwaltung von Aktionsgutscheinen - Struktur</value>
  </data>
  <data name="table_header_id" xml:space="preserve">
    <value>ID</value>
  </data>
  <data name="table_header_label" xml:space="preserve">
    <value>Bezeichnung</value>
  </data>
  <data name="table_header_last_name" xml:space="preserve">
    <value>Nachname</value>
  </data>
  <data name="table_header_first_name" xml:space="preserve">
    <value>Vorname</value>
  </data>
  <data name="table_header_creation_date" xml:space="preserve">
    <value>Erstellungsdatum</value>
  </data>
  <data name="table_header_coupon_status" xml:space="preserve">
    <value>Gutscheinstatus</value>
  </data>
  <data name="table_header_actions" xml:space="preserve">
    <value>Aktionen</value>
  </data>
  <data name="coupon_status_active" xml:space="preserve">
    <value>aktive Gutschein(e)</value>
  </data>
  <data name="coupon_status_expired" xml:space="preserve">
    <value>Gutscheinstatus abgelaufen</value>
  </data>
  <data name="coupon_status_none" xml:space="preserve">
    <value>Kein Gutschein</value>
  </data>
  <data name="button_add_coupons" xml:space="preserve">
    <value>Gutscheine hinzufügen</value>
  </data>
  <data name="button_view_coupons" xml:space="preserve">
    <value>Gutscheine anzeigen</value>
  </data>
  <data name="button_export" xml:space="preserve">
    <value>Exportieren</value>
  </data>
  <data name="button_generate" xml:space="preserve">
    <value>Gutscheine generieren</value>
  </data>
  <data name="button_close" xml:space="preserve">
    <value>Schließen</value>
  </data>
  <data name="modal_create_title" xml:space="preserve">
    <value>Aktionsgutscheine erstellen für</value>
  </data>
  <data name="modal_number_of_coupons" xml:space="preserve">
    <value>Anzahl der zu generierenden Gutscheine</value>
  </data>
  <data name="modal_max_coupons_message" xml:space="preserve">
    <value>Sie können bis zu 20 Gutscheine auf einmal generieren</value>
  </data>
  <data name="modal_start_date" xml:space="preserve">
    <value>Startdatum</value>
  </data>
  <data name="modal_start_date_error" xml:space="preserve">
    <value>Das Startdatum muss heute oder in der Zukunft liegen</value>
  </data>
  <data name="modal_end_date" xml:space="preserve">
    <value>Gemeinsames Ablaufdatum</value>
  </data>
  <data name="modal_end_date_error" xml:space="preserve">
    <value>Das Ablaufdatum muss nach dem Startdatum liegen</value>
  </data>
  <data name="modal_generating" xml:space="preserve">
    <value>Generierung...</value>
  </data>
  <data name="modal_coupons_title" xml:space="preserve">
    <value>Aktionsgutscheine für</value>
  </data>
  <data name="tab_active_coupons" xml:space="preserve">
    <value>Aktive Gutscheine</value>
  </data>
  <data name="tab_expired_coupons" xml:space="preserve">
    <value>Abgelaufene Gutscheine</value>
  </data>
  <data name="tab_all_coupons" xml:space="preserve">
    <value>Alle Gutscheine</value>
  </data>
  <data name="tab_no_coupons" xml:space="preserve">
    <value>Keine Gutscheine für dieses Profil gefunden.</value>
  </data>
  <data name="coupon_list_code" xml:space="preserve">
    <value>Code</value>
  </data>
  <data name="coupon_list_creation_date" xml:space="preserve">
    <value>Erstellungsdatum</value>
  </data>
  <data name="coupon_list_start_date" xml:space="preserve">
    <value>Startdatum</value>
  </data>
  <data name="coupon_list_expiration_date" xml:space="preserve">
    <value>Ablaufdatum</value>
  </data>
  <data name="coupon_list_expires_in" xml:space="preserve">
    <value>Läuft ab in</value>
  </data>
  <data name="coupon_list_expired_since" xml:space="preserve">
    <value>Abgelaufen seit</value>
  </data>
  <data name="coupon_list_status" xml:space="preserve">
    <value>Status</value>
  </data>
  <data name="coupon_list_active" xml:space="preserve">
    <value>Aktiv</value>
  </data>
  <data name="coupon_list_expired" xml:space="preserve">
    <value>Tag(e)</value>
  </data>
  <data name="coupon_list_days" xml:space="preserve">
    <value>Tag(e)</value>
  </data>
  <data name="coupon_list_no_active_coupons" xml:space="preserve">
    <value>Keine aktiven Gutscheine</value>
  </data>
  <data name="coupon_list_no_expired_coupons" xml:space="preserve">
    <value>Keine abgelaufenen Gutscheine</value>
  </data>
  <data name="button_export_these_coupons" xml:space="preserve">
    <value>Diese Gutscheine exportieren</value>
  </data>
  <data name="modal_success_title" xml:space="preserve">
    <value>Aktionsgutscheine erfolgreich generiert für</value>
  </data>
  <data name="button_exporting" xml:space="preserve">
    <value>Exportieren...</value>
  </data>
  <data name="coupon_aucun_oupon_cree" xml:space="preserve">
    <value>Kein Gutschein konnte erstellt werden. Bitte versuchen Sie es erneut.</value>
  </data>
  <data name="coupon_erreur_creation" xml:space="preserve">
    <value>Fehler beim Erstellen der Gutscheine: {0}</value>
  </data>
  <data name="coupon_creation_succes" xml:space="preserve">
    <value>{0} Gutschein(e) erfolgreich für {1} erstellt.</value>
  </data>
  <data name="create_coupon_attention" xml:space="preserve">
    <value>Achtung</value>
  </data>
  <data name="profil_acheteur_non_valide" xml:space="preserve">
    <value>Ungültiges Käuferprofil.</value>
  </data>
  <data name="erreur_chargement_donnees" xml:space="preserve">
    <value>Fehler beim Laden der Daten: {0}</value>
  </data>
  <data name="erreur_chargement_coupons" xml:space="preserve">
    <value>Fehler beim Laden der Gutscheine: {0}</value>
  </data>
  <data name="erreur_exportation_coupons" xml:space="preserve">
    <value>Fehler beim Exportieren von Gutscheinen: {0}</value>
  </data>
  <data name="coupon_export_success" xml:space="preserve">
    <value>Die Gutscheine von {0} wurden erfolgreich exportiert.</value>
  </data>
  <data name="aucun_coupon_exporter" xml:space="preserve">
    <value>Keine Gutscheine zum Exportieren für {0}.</value>
  </data>
  <data name="erreur_telechargement_fichier" xml:space="preserve">
    <value>Fehler beim Herunterladen der Datei: {0}</value>
  </data>
  <data name="nombre_coupons_range" xml:space="preserve">
    <value>Die Anzahl der Gutscheine</value>
  </data>
  <data name="nombre_coupons_required" xml:space="preserve">
    <value>Die Anzahl der Gutscheine ist erforderlich.</value>
  </data>
  <data name="date_debut_required" xml:space="preserve">
    <value>Das Startdatum ist erforderlich.</value>
  </data>
  <data name="date_expiration_required" xml:space="preserve">
    <value>Das Ablaufdatum ist erforderlich.</value>
  </data>
  <data name="config_keyuniquecodepromo_missing" xml:space="preserve">
    <value>Der Konfigurationsschlüssel 'keyUniqueCodePromo' fehlt oder ist leer</value>
  </data>
  <data name="coupon_list_no_used_coupons" xml:space="preserve">
    <value>Liste des coupons – Aucun coupon utilisé</value>
  </data>
  <data name="coupon_status_used" xml:space="preserve">
    <value> Gutscheinstatus verwendet</value>
  </data>
  <data name="tab_used_coupons" xml:space="preserve">
    <value>Verwendete Gutscheine</value>
  </data>
  <data name="coupon_list_commande_id" xml:space="preserve">
    <value>Bestell-ID</value>
  </data>
  <data name="coupon_list_used_date" xml:space="preserve">
    <value>Verwendungsdatum</value>
  </data>
  <data name="modal_prefix" xml:space="preserve">
    <value>Präfix</value>
  </data>
  <data name="modal_example_code" xml:space="preserve">
    <value>Beispielcode</value>
  </data>
  <data name="modal_format_explanation" xml:space="preserve">
    <value>Format: Präfix + 2 Ziffern + 3 Buchstaben</value>
  </data>
  <data name="coupon_list_used" xml:space="preserve">
    <value>Gutschein verwendet</value>
  </data>
  <data name="modal_prefix_min_length" xml:space="preserve">
    <value>Das Präfix muss mindestens 2 Zeichen enthalten</value>
  </data>
  <data name="service-inclusion-exclusion" xml:space="preserve">
    <value>Inklusion/Ausschluss</value>
  </data>
  <data name="services_inclusion_exclusion" xml:space="preserve">
    <value>Dienste für Ein- und Ausschluss</value>
  </data>
  <data name="services_inclusion_exclusion_info" xml:space="preserve">
    <value>Verwalten Sie eingeschlossene und ausgeschlossene Strukturen für verschiedene Dienste in PREPROD- und PROD-Umgebungen.</value>
  </data>
  <data name="creation_commande" xml:space="preserve">
    <value>Bestellerstellung</value>
  </data>
  <data name="edition_commande" xml:space="preserve">
    <value>Bestellbearbeitung</value>
  </data>
  <data name="paiement_commande" xml:space="preserve">
    <value>Bestellzahlung</value>
  </data>
  <data name="envoi_mail" xml:space="preserve">
    <value>E-Mail-Versand</value>
  </data>
  <data name="envoi_mail_info" xml:space="preserve">
    <value>E-Mail-Info-Versand</value>
  </data>
  <data name="preprod_environment" xml:space="preserve">
    <value>PREPROD-Umgebung</value>
  </data>
  <data name="prod_environment" xml:space="preserve">
    <value>PROD-Umgebung</value>
  </data>
  <data name="structure_id" xml:space="preserve">
    <value>Struktur-ID</value>
  </data>
  <data name="enter_structure_id" xml:space="preserve">
    <value>Struktur-ID eingeben</value>
  </data>
  <data name="inclusion" xml:space="preserve">
    <value>Einschluss</value>
  </data>
  <data name="exclusion" xml:space="preserve">
    <value>Ausschluss</value>
  </data>
  <data name="add_structure" xml:space="preserve">
    <value>Struktur hinzufügen</value>
  </data>
  <data name="inclusions" xml:space="preserve">
    <value>Einschlüsse</value>
  </data>
  <data name="exclusions" xml:space="preserve">
    <value>Ausschlüsse</value>
  </data>
  <data name="no_structures" xml:space="preserve">
    <value>Keine Strukturen</value>
  </data>
  <data name="common_warning" xml:space="preserve">
    <value>Warnung</value>
  </data>
  <data name="common_info" xml:space="preserve">
    <value>Information</value>
  </data>
  <data name="structure_added_successfully" xml:space="preserve">
    <value>Structure ajoutée avec succès</value>
  </data>
  <data name="structure_already_exists" xml:space="preserve">
    <value>Struktur existiert bereits</value>
  </data>
  <data name="structure_removed_successfully" xml:space="preserve">
    <value>Struktur erfolgreich entfernt</value>
  </data>
  <data name="structure_not_found" xml:space="preserve">
    <value>Struktur nicht gefunden</value>
  </data>
  <data name="file_does_not_exist" xml:space="preserve">
    <value>Datei existiert nicht</value>
  </data>
  <data name="no_permission_write" xml:space="preserve">
    <value>Sie haben keine Schreibrechte</value>
  </data>
  <data name="selected_structure_status" xml:space="preserve">
    <value> statusStatus der ausgewählten Struktur</value>
  </data>
  <data name="select_structure_first" xml:space="preserve">
    <value>Wählen Sie zuerst eine Struktur aus</value>
  </data>
  <data name="select_structure_option" xml:space="preserve">
    <value>Struktur auswählen</value>
  </data>
  <data name="service_inclusion_exclusion_blocked" xml:space="preserve">
    <value>Blockiert</value>
  </data>
  <data name="service_inclusion_exlusion_blocked_status" xml:space="preserve">
    <value>Blockierungsstatus</value>
  </data>
  <data name="structure_status_recap" xml:space="preserve">
    <value>Strukturstatusbericht</value>
  </data>
  <data name="service_inclusion_exclusion_name" xml:space="preserve">
    <value>Dienstname</value>
  </data>
  <data name="service_blocked_status" xml:space="preserve">
    <value>Sperrstatus</value>
  </data>
  <data name="structure_included" xml:space="preserve">
    <value>Struktur eingeschlossen</value>
  </data>
  <data name="structure_excluded" xml:space="preserve">
    <value>Struktur ausgeschlossen</value>
  </data>
  <data name="structure_default_status" xml:space="preserve">
    <value>Standardstatus</value>
  </data>
  <data name="not_blocked" xml:space="preserve">
    <value>Nicht gesperrt</value>
  </data>
  <data name="structure_selected" xml:space="preserve">
    <value>Ausgewählte Struktur</value>
  </data>
  <data name="structure_removed_from_exclusion_prod" xml:space="preserve">
    <value>Struktur {0} aus exclusionsStructures.xml PROD entfernt</value>
  </data>
  <data name="structure_removed_from_inclusion_preprod" xml:space="preserve">
    <value> Struktur {0} aus inclusionsStructures.xml PREPROD entfernt</value>
  </data>
  <data name="structure_added_to_exclusion_prod" xml:space="preserve">
    <value>Struktur {0} in exclusionsStructures.xml PROD hinzugefügt</value>
  </data>
  <data name="structure_added_to_inclusion_preprod" xml:space="preserve">
    <value>Struktur {0} in inclusionsStructures.xml PREPROD hinzugefügt</value>
  </data>
  <data name="error_generic" xml:space="preserve">
    <value> Fehler: {0}</value>
  </data>
  <data name="structure_no_data_found" xml:space="preserve">
    <value>Es wurde keine Struktur in der Datenbank gefunden.</value>
  </data>
  <data name="structure_loading_error" xml:space="preserve">
    <value> Fehler beim Laden der Strukturen: {0}</value>
  </data>
  <data name="xml_loading_error_for_service" xml:space="preserve">
    <value>Fehler beim Laden der XML-Dateien für den Dienst {0}: {1}</value>
  </data>
  <data name="structure_name" xml:space="preserve">
    <value> Strukturname</value>
  </data>
  <data name="transfere-pointage-photo" xml:space="preserve">
    <value>Foto-Stempelübertragung</value>
  </data>
  <data name="transfert_pointagephoto_titre_page" xml:space="preserve">
    <value>Foto-Markierung {0}{1}</value>
  </data>
  <data name="transfert_pointagephoto_label_source" xml:space="preserve">
    <value> Quell-Standort (mit Bild):</value>
  </data>
  <data name="transfert_pointagephoto_option_source_defaut" xml:space="preserve">
    <value>-- Wählen Sie einen Quellstandort aus --</value>
  </data>
  <data name="transfert_pointagephoto_label_cible" xml:space="preserve">
    <value> Zielstandort:</value>
  </data>
  <data name="transfert_pointagephoto_option_cible_defaut" xml:space="preserve">
    <value> -- Wählen Sie einen Zielstandort aus --</value>
  </data>
  <data name="transfert_pointagephoto_bouton_transferer" xml:space="preserve">
    <value>Übertragen</value>
  </data>
  <data name="transfert_pointagephoto_bouton_traitement" xml:space="preserve">
    <value>Wird verarbeitet...</value>
  </data>
  <data name="transfert_pointagephoto_bouton_retour" xml:space="preserve">
    <value>Zurück</value>
  </data>
  <data name="transfert_pointagephoto_label_chargement" xml:space="preserve">
    <value>Wird geladen...</value>
  </data>
  <data name="transfert_pointagephoto_erreur_chargement" xml:space="preserve">
    <value>Beim Laden der physischen Standorte ist ein Fehler aufgetreten: {0}</value>
  </data>
  <data name="transfert_pointagephoto_erreur_source" xml:space="preserve">
    <value>Bitte wählen Sie einen Quellstandort aus.</value>
  </data>
  <data name="transfert_pointagephoto_erreur_cible" xml:space="preserve">
    <value> Bitte wählen Sie einen Zielstandort aus.</value>
  </data>
  <data name="transfert_pointagephoto_succes_transfert" xml:space="preserve">
    <value>Die Übertragung des Foto-Zeitstempels war erfolgreich vom Standort {0} zu {1}.</value>
  </data>
  <data name="transfert_pointagephoto_echec_transfert" xml:space="preserve">
    <value>Übertragung fehlgeschlagen. Stellen Sie sicher, dass der Quellstandort ein Bild hat.</value>
  </data>
  <data name="transfert_pointagephoto_exception_transfert" xml:space="preserve">
    <value>Beim Übertragen ist ein Fehler aufgetreten: {0}</value>
  </data>
  <data name="transfert_pointagephoto_succes_chargement" xml:space="preserve">
    <value> Daten erfolgreich geladen</value>
  </data>
  <data name="button.generate_xml_file.label" xml:space="preserve">
    <value>Benutzerdefinierte XML-Datei generieren</value>
  </data>
  <data name="button.generate_xml_file.loading" xml:space="preserve">
    <value>Benutzerdefinierte XML-Datei wird generiert...</value>
  </data>
  <data name="xml_generation_environment_undefined" xml:space="preserve">
    <value>Die Umgebung ist in der Konfiguration nicht definiert.</value>
  </data>
  <data name="xml_generation_success" xml:space="preserve">
    <value>XML-Datei erfolgreich generiert.</value>
  </data>
  <data name="modal_format" xml:space="preserve">
    <value>Format</value>
  </data>
  <data name="xml_generation_error" xml:space="preserve">
    <value>Fehler beim Generieren der XML-Datei: {0}</value>
  </data>
  <data name="xml_generation_structureid_null" xml:space="preserve">
    <value>StructureId ist null, Generierung nicht möglich.</value>
  </data>
  <data name="transfert_vignette_titre_section" xml:space="preserve">
    <value>Miniaturansicht-Übertragung</value>
  </data>
  <data name="transfert_vignette_label_source" xml:space="preserve">
    <value>Physischer Quellstandort</value>
  </data>
  <data name="transfert_vignette_option_source_defaut" xml:space="preserve">
    <value>Wählen Sie den physischen Quellstandort</value>
  </data>
  <data name="transfert_vignette_label_cible" xml:space="preserve">
    <value>Physischer Zielstandort</value>
  </data>
  <data name="transfert_vignette_option_cible_defaut" xml:space="preserve">
    <value>Wählen Sie den physischen Zielstandort</value>
  </data>
  <data name="transfert_vignette_bouton_transferer" xml:space="preserve">
    <value>Miniaturansichten übertragen</value>
  </data>
  <data name="transfert_vignette_bouton_traitement" xml:space="preserve">
    <value>Verarbeitung läuft...</value>
  </data>
  <data name="transfert_pointagephoto_titre_section" xml:space="preserve">
    <value>Foto-Markierungsübertragung auf (x,y)</value>
  </data>
  <data name="modal_format_help" xml:space="preserve">
    <value>Format-Hilfe </value>
  </data>
  <data name="modal_example_code_help" xml:space="preserve">
    <value>Hilfe zum Beispielcode</value>
  </data>
  <data name="couponstatus_active" xml:space="preserve">
    <value> Aktiv</value>
  </data>
  <data name="couponstatus_expired" xml:space="preserve">
    <value> Abgelaufen</value>
  </data>
  <data name="couponstatus_used" xml:space="preserve">
    <value>Verwendet</value>
  </data>
  <data name="couponstatus_undefined" xml:space="preserve">
    <value>Nicht definiert</value>
  </data>
  <data name="search_off_no_translation_found_title" xml:space="preserve">
    <value> Keine Übersetzung gefunden</value>
  </data>
  <data name="search_off_try_changing_search_criteria_message" xml:space="preserve">
    <value>Versuchen Sie, Ihre Suchkriterien zu ändern.</value>
  </data>
  <data name="search_off_missing_translation_notice" xml:space="preserve">
    <value>Fehlende Übersetzung</value>
  </data>
  <data name="voir_champs_vides_langue" xml:space="preserve">
    <value>Leere Felder der Sprache anzeigen</value>
  </data>
  <data name="modal.max_possible_number" xml:space="preserve">
    <value>Maximal mögliche Anzahl:</value>
  </data>
  <data name="modal.warning_too_many_coupons" xml:space="preserve">
    <value>Die maximale Anzahl möglicher Gutscheine übersteigt 2000. Wenn Sie mehr als 2000 Gutscheine generieren, dauert es länger.
Wir empfehlen, jeweils nur 2000 Gutscheine zu generieren.</value>
  </data>
  <data name="modal.confirm_generation_more_than_2000" xml:space="preserve">
    <value>Sie haben darum gebeten, mehr als 2000 Gutscheine zu generieren. Die Generierung kann länger dauern. Möchten Sie fortfahren?</value>
  </data>
  <data name="toast.adjustment_done" xml:space="preserve">
    <value>Anpassung vorgenommen</value>
  </data>
  <data name="toast.coupons_reduced_to_2000" xml:space="preserve">
    <value>Die Anzahl der Gutscheine wurde auf 2000 reduziert.</value>
  </data>
  <data name="toast.number_adjusted" xml:space="preserve">
    <value>Anzahl angepasst</value>
  </data>
  <data name="toast.coupons_reduced_to_x" xml:space="preserve">
    <value>Die Anzahl der Gutscheine wurde auf {0} reduziert.</value>
  </data>
  <data name="transfert_pointagephoto_bouton_verifier" xml:space="preserve">
    <value> Überprüfen</value>
  </data>
  <data name="transfert_pointagephoto_bouton_generer_xml" xml:space="preserve">
    <value> Benutzerdefinierte XML-Datei generieren</value>
  </data>
  <data name="transfert_resultats_incoherences_titre" xml:space="preserve">
    <value>Unstimmigkeiten erkannt:</value>
  </data>
  <data name="transfert_resultats_source_unique" xml:space="preserve">
    <value>in der Quelle vorhanden, aber nicht im Ziel</value>
  </data>
  <data name="transfert_resultats_cible_unique" xml:space="preserve">
    <value>im Ziel vorhanden, aber nicht in der Quelle</value>
  </data>
  <data name="transfert_resultats_plus_elements" xml:space="preserve">
    <value>+ {0} weitere nicht angezeigte</value>
  </data>
  <data name="transfert_vignette_bouton_verifier" xml:space="preserve">
    <value> Überprüfen</value>
  </data>
  <data name="transfert_vignette_bouton_generer_xml" xml:space="preserve">
    <value> XML-Datei für Abzeichen generieren</value>
  </data>
  <data name="transfert_resultats_identiques_vignette" xml:space="preserve">
    <value>Die physischen Standorte sind identisch.</value>
  </data>
  <data name="transfert_pointagephoto_verification_selection_lieux" xml:space="preserve">
    <value>Wählen Sie einen Quell- und Zielort zum Überprüfen.</value>
  </data>
  <data name="transfert_pointagephoto_verification_incoherences_detectees" xml:space="preserve">
    <value>Unstimmigkeiten wurden festgestellt.</value>
  </data>
  <data name="transfert_pointagephoto_verification_compatibles" xml:space="preserve">
    <value> Die physischen Standorte sind kompatibel.</value>
  </data>
  <data name="transfert_pointagephoto_verification_erreur" xml:space="preserve">
    <value>Fehler bei der Überprüfung</value>
  </data>
  <data name="transfert_pointagephoto_transfert_incoherences_a_corriger" xml:space="preserve">
    <value>Beheben Sie die Unstimmigkeiten vor dem Übertragen.</value>
  </data>
  <data name="transfert_pointagephoto_transfert_selection_lieux" xml:space="preserve">
    <value>Wählen Sie einen Quell- und Zielort.</value>
  </data>
  <data name="transfert_pointagephoto_export_structure_id_manquant" xml:space="preserve">
    <value> Struktur-ID fehlt.</value>
  </data>
  <data name="transfert_pointagephoto_export_environnement_indefini" xml:space="preserve">
    <value>Umgebung nicht definiert.</value>
  </data>
  <data name="transfert_pointagephoto_export_xml_succes" xml:space="preserve">
    <value>XML-Datei erfolgreich generiert.</value>
  </data>
  <data name="transfert_pointagephoto_export_xml_erreur" xml:space="preserve">
    <value>Fehler beim Generieren der XML-Datei: {0}</value>
  </data>
  <data name="transfert_pointagephoto_export_salle_structure_id_manquant" xml:space="preserve">
    <value>Struktur-ID fehlt.</value>
  </data>
  <data name="transfert_pointagephoto_export_salle_environnement_indefini" xml:space="preserve">
    <value>Umgebung nicht definiert.</value>
  </data>
  <data name="transfert_pointagephoto_export_salle_xml_succes" xml:space="preserve">
    <value>XML-Datei erfolgreich generiert.</value>
  </data>
  <data name="transfert_pointagephoto_export_salle_xml_erreur" xml:space="preserve">
    <value>Fehler beim Generieren der XML-Datei: {0}</value>
  </data>
  <data name="transfert_pointagephoto_incoherence_source" xml:space="preserve">
    <value>Rang {0}, Sitz {1} – in der Quelle vorhanden, aber nicht im Ziel</value>
  </data>
  <data name="transfert_pointagephoto_incoherence_source_autres" xml:space="preserve">
    <value>+ {0} weitere Elemente nicht angezeigt.</value>
  </data>
  <data name="transfert_pointagephoto_incoherence_cible" xml:space="preserve">
    <value>Rang {0}, Sitz {1} – im Ziel vorhanden, aber nicht in der Quelle</value>
  </data>
  <data name="Rang {0}, siège {1} — présent dans la cible mais pas dans la source" xml:space="preserve">
    <value>+ {0} weitere Elemente nicht angezeigt.</value>
  </data>
  <data name="transfert_pointagephoto_incoherence_cible_autres" xml:space="preserve">
    <value>+ {0} weitere Elemente nicht angezeigt.</value>
  </data>
  <data name="rolemanagement_edit_success_title" xml:space="preserve">
    <value>Bearbeitung erfolgreich</value>
  </data>
  <data name="rolemanagement_edit_success_message" xml:space="preserve">
    <value>Das Modul wurde erfolgreich bearbeitet.</value>
  </data>
  <data name="rolemanagement_edit_error_title" xml:space="preserve">
    <value>Fehler beim Bearbeiten</value>
  </data>
  <data name="rolemanagement_edit_error_message" xml:space="preserve">
    <value>Beim Bearbeiten ist ein Fehler aufgetreten: {0}</value>
  </data>
  <data name="rolemanagement_delete_success_title" xml:space="preserve">
    <value> Erfolgreich gelöscht</value>
  </data>
  <data name="rolemanagement_delete_success_messag" xml:space="preserve">
    <value>Le module a été supprimé avec succès.</value>
  </data>
  <data name="rolemanagement_delete_error_title" xml:space="preserve">
    <value>Fehler beim Löschen</value>
  </data>
  <data name="rolemanagement_delete_error_message" xml:space="preserve">
    <value>Une erreur s’est produite lors de la suppression : {0}</value>
  </data>
  <data name="rolemanagement_actions" xml:space="preserve">
    <value>Aktionen</value>
  </data>
  <data name="rolemanagement_edit" xml:space="preserve">
    <value>Modul bearbeiten</value>
  </data>
  <data name="rolemanagement_delete" xml:space="preserve">
    <value> Löschen</value>
  </data>
  <data name="rolemanagement_module_name" xml:space="preserve">
    <value>Modulname</value>
  </data>
  <data name="rolemanagement_module_comment" xml:space="preserve">
    <value>Modulkommentar</value>
  </data>
  <data name="rolemanagement_save" xml:space="preserve">
    <value>Speichern</value>
  </data>
  <data name="rolemanagement_cancel" xml:space="preserve">
    <value> Abbrechen</value>
  </data>
  <data name="rolemanagement_edit_module" xml:space="preserve">
    <value>Modul bearbeiten</value>
  </data>
  <data name="home_outil_gesion_tst" xml:space="preserve">
    <value> Greifen Sie schnell auf die wichtigsten Funktionen Ihrer Plattform zu</value>
  </data>
  <data name="home_btn_primary_acceder" xml:space="preserve">
    <value>Zugreifen</value>
  </data>
  <data name="home_worflow_questions" xml:space="preserve">
    <value>Bereit, Ihren Workflow zu optimieren?</value>
  </data>
  <data name="home_explorer_fonctionnalite" xml:space="preserve">
    <value>Erkunden Sie die oben verfügbaren Funktionen, um sofort Ihre Produktivität zu steigern.</value>
  </data>
  <data name="cliquez_sur_les_cartes_pour_naviguer" xml:space="preserve">
    <value>Klicken Sie auf die Karten, um zu navigieren</value>
  </data>
  <data name="stats_support_continu" xml:space="preserve">
    <value> Durchgehender Support</value>
  </data>
  <data name="stats_productivite" xml:space="preserve">
    <value>Produktivität</value>
  </data>
  <data name="stats_optimise" xml:space="preserve">
    <value> Optimiert</value>
  </data>
  <data name="stats_possibilites" xml:space="preserve">
    <value> Möglichkeitenx</value>
  </data>
  <data name="gestion-maquette-abo-fermer" xml:space="preserve">
    <value>Vorlagenverwaltung (Abo)</value>
  </data>
  <data name="gestionmaquette_title" xml:space="preserve">
    <value>Vorlagenverwaltung für geschlossenes Abo</value>
  </data>
  <data name="gestionmaquette_loading" xml:space="preserve">
    <value>Wird geladen...</value>
  </data>
  <data name="gestionmaquette_subscription_formule" xml:space="preserve">
    <value>Abonnement-Formel</value>
  </data>
  <data name="gestionmaquette_select_formula" xml:space="preserve">
    <value>Formel auswählen</value>
  </data>
  <data name="gestionmaquette_session" xml:space="preserve">
    <value>Sitzung</value>
  </data>
  <data name="gestionmaquette_select_session" xml:space="preserve">
    <value>Sitzung auswählen</value>
  </data>
  <data name="gestionmaquette_template" xml:space="preserve">
    <value>Vorlage</value>
  </data>
  <data name="gestionmaquette_select_template" xml:space="preserve">
    <value>Vorlage auswählen</value>
  </data>
  <data name="gestionmaquette_save" xml:space="preserve">
    <value>Speichern</value>
  </data>
  <data name="gestionmaquette_existing_associations" xml:space="preserve">
    <value>Bestehende Zuordnungen</value>
  </data>
  <data name="gestionmaquette_refresh" xml:space="preserve">
    <value>Aktualisieren</value>
  </data>
  <data name="gestionmaquette_no_associations_found" xml:space="preserve">
    <value>Keine Zuordnung gefunden</value>
  </data>
  <data name="gestionmaquette_formule" xml:space="preserve">
    <value>Formel</value>
  </data>
  <data name="gestionmaquette_operation_date" xml:space="preserve">
    <value>Vorgangsdatum</value>
  </data>
  <data name="gestionmaquette_actions" xml:space="preserve">
    <value>Aktionen</value>
  </data>
  <data name="gestionmaquetteabofermer_message_error_loading_data" xml:space="preserve">
    <value>Fehler beim Laden der Daten</value>
  </data>
  <data name="gestionmaquetteabofermer_message_error_loading_associations" xml:space="preserve">
    <value> Fehler beim Laden der Zuordnungen</value>
  </data>
  <data name="gestionmaquetteabofermer_message_please_select_all" xml:space="preserve">
    <value> Bitte wählen Sie alle Elemente aus</value>
  </data>
  <data name="gestionmaquetteabofermer_message_association_already_exists" xml:space="preserve">
    <value>Diese Zuordnung existiert bereits</value>
  </data>
  <data name="gestionmaquetteabofermer_message_association_saved_successfully" xml:space="preserve">
    <value>Zuordnung erfolgreich gespeichert</value>
  </data>
  <data name="gestionmaquetteabofermer_message_error_saving_association" xml:space="preserve">
    <value>Fehler beim Speichern der Zuordnung</value>
  </data>
  <data name="gestionmaquetteabofermer_message_association_deleted_successfully" xml:space="preserve">
    <value>Zuordnung erfolgreich gelöscht</value>
  </data>
  <data name="gestionmaquetteabofermer_message_error_deleting_association" xml:space="preserve">
    <value>Fehler beim Löschen der Zuordnung</value>
  </data>
  <data name="gestionmaquetteabofermer_message_error" xml:space="preserve">
    <value>Fehler</value>
  </data>
  <data name="gestionmaquette_new_association" xml:space="preserve">
    <value>Neue Verknüpfung zwischen Layout, Sitzung und Abo</value>
  </data>
  <data name="preparation-mise-vente" xml:space="preserve">
    <value>Verkaufsvorbereitung</value>
  </data>
  <data name="clean_tables_success_message" xml:space="preserve">
    <value>Die Tabellen wurden erfolgreich bereinigt. Alle Warteschlangendaten wurden gelöscht.</value>
  </data>
  <data name="clean_tables_failed_message" xml:space="preserve">
    <value> Die Bereinigung konnte nicht durchgeführt werden. Es sind aktuelle Daten vorhanden.</value>
  </data>
  <data name="clean_tables_error_message" xml:space="preserve">
    <value>Fehler beim Bereinigen: {0}</value>
  </data>
  <data name="check_cleanup_no_recent_data_message" xml:space="preserve">
    <value>Kein kürzlicher Durchgang erkannt. Die Reinigung kann sicher durchgeführt werden.</value>
  </data>
  <data name="check_cleanup_recent_data_warning" xml:space="preserve">
    <value>Achtung: Es wurden aktuelle Daten (weniger als 1 Stunde) erkannt. Aus Sicherheitsgründen ist keine Bereinigung möglich.</value>
  </data>
  <data name="check_cleanup_error_message" xml:space="preserve">
    <value>Fehler bei der Überprüfung: {0}</value>
  </data>
  <data name="on_initialized_click_to_verify_message" xml:space="preserve">
    <value>Klicken Sie auf „Erneut prüfen“, um den Status der Durchgänge vor der Reinigung zu überprüfen.</value>
  </data>
  <data name="preparation_mise_vente_title" xml:space="preserve">
    <value>Verkaufs­vorbereitung</value>
  </data>
  <data name="preparation_mise_vente_structure_label" xml:space="preserve">
    <value> Struktur: {0} ({1})</value>
  </data>
  <data name="preparation_mise_vente_loading" xml:space="preserve">
    <value>Datenprüfung läuft...</value>
  </data>
  <data name="preparation_mise_vente_success_message" xml:space="preserve">
    <value>Tabellen wurden erfolgreich bereinigt. Alle Warteschlangendaten wurden gelöscht.</value>
  </data>
  <data name="preparation_mise_vente_error_message" xml:space="preserve">
    <value>Bereinigung konnte nicht durchgeführt werden. Es sind aktuelle Daten vorhanden.</value>
  </data>
  <data name="preparation_mise_vente_check_error" xml:space="preserve">
    <value>Fehler bei der Überprüfung: {0}</value>
  </data>
  <data name="preparation_mise_vente_check_success" xml:space="preserve">
    <value>Kein kürzlicher Durchgang erkannt. Die Reinigung kann sicher durchgeführt werden.</value>
  </data>
  <data name="preparation_mise_vente_check_warning" xml:space="preserve">
    <value>Achtung: Aktuelle Daten (weniger als 1 Stunde) erkannt. Bereinigung nicht möglich.</value>
  </data>
  <data name="preparation_mise_vente_click_verify" xml:space="preserve">
    <value>Klicken Sie auf „Erneut prüfen“, um den Status der Durchgänge vor der Reinigung zu überprüfen.</value>
  </data>
  <data name="preparation_mise_vente_verification_status" xml:space="preserve">
    <value>Überprüfungsstatus</value>
  </data>
  <data name="preparation_mise_vente_not_verified" xml:space="preserve">
    <value>Nicht überprüft</value>
  </data>
  <data name="preparation_mise_vente_not_verified_description" xml:space="preserve">
    <value>Der Status der Durchgänge wurde noch nicht überprüft.</value>
  </data>
  <data name="preparation_mise_vente_not_verified_hint" xml:space="preserve">
    <value>Klicken Sie auf „Erneut prüfen“, um die Daten zu überprüfen.</value>
  </data>
  <data name="preparation_mise_vente_recent_data" xml:space="preserve">
    <value>Kürzliche Durchgänge erkannt</value>
  </data>
  <data name="preparation_mise_vente_recent_data_description" xml:space="preserve">
    <value>Durchgänge, die jünger als eine Stunde sind, befinden sich in der Tabelle queuing.</value>
  </data>
  <data name="preparation_mise_vente_recent_data_hint" xml:space="preserve">
    <value>Bereinigung ist derzeit aus Sicherheitsgründen blockiert.</value>
  </data>
  <data name="preparation_mise_vente_ready_to_cleanup" xml:space="preserve">
    <value> Bereit zur Bereinigung</value>
  </data>
  <data name="preparation_mise_vente_no_recent_data" xml:space="preserve">
    <value>Kein kürzlicher Durchgang erkannt.</value>
  </data>
  <data name="preparation_mise_vente_safe_to_cleanup" xml:space="preserve">
    <value> Tabellenbereinigung kann sicher durchgeführt werden.</value>
  </data>
  <data name="preparation_mise_vente_actions" xml:space="preserve">
    <value>Verfügbare Aktionen</value>
  </data>
  <data name="preparation_mise_vente_check_button" xml:space="preserve">
    <value> Erneut prüfen</value>
  </data>
  <data name="preparation_mise_vente_check_button_hin" xml:space="preserve">
    <value>Aktualisiert den Überprüfungsstatus</value>
  </data>
  <data name="preparation_mise_vente_cleanup_required" xml:space="preserve">
    <value>Überprüfung erforderlich</value>
  </data>
  <data name="preparation_mise_vente_cleanup_blocked" xml:space="preserve">
    <value> Bereinigung blockier</value>
  </data>
  <data name="preparation_mise_vente_cleanup_button" xml:space="preserve">
    <value>Tabellen bereinigen</value>
  </data>
  <data name="preparation_mise_vente_cleanup_button_hint" xml:space="preserve">
    <value>Löscht alle Daten aus den Warteschlangentabellen</value>
  </data>
  <data name="preparation_mise_vente_cleanup_warning" xml:space="preserve">
    <value>Die Bereinigung löscht alle Daten aus queuing_{0} und queuing_{0}_histopassages.</value>
  </data>
  <data name="preparation_mise_vente_cleanup_irreversible" xml:space="preserve">
    <value>Warten Sie, bis die kürzlichen Durchgänge älter als eine Stunde sind oder bis keine Benutzer mehr verbunden sind.</value>
  </data>
  <data name="preparation_mise_vente_verify_first" xml:space="preserve">
    <value>Bitte überprüfen Sie zuerst den Status der Durchgänge, bevor Sie mit der Reinigung fortfahren.</value>
  </data>
  <data name="preparation_mise_vente_verify_purpose" xml:space="preserve">
    <value>Diese Überprüfung stellt sicher, dass keine aktuellen Daten verloren gehen.</value>
  </data>
  <data name="preparation_mise_vente_check_button_hint" xml:space="preserve">
    <value> Aktualisiert den Überprüfungsstatus</value>
  </data>
  <data name="preparation_mise_vente_cancel_button" xml:space="preserve">
    <value> Abbrechen</value>
  </data>
  <data name="preparation_mise_vente_cleanup_confirmation_title" xml:space="preserve">
    <value>Bestätigungsbereinigung</value>
  </data>
  <data name="preparation_mise_vente_advice_wait_or_disconnect" xml:space="preserve">
    <value>Warten Sie, bis die kürzlichen Durchgänge älter als eine Stunde sind.</value>
  </data>
  <data name="tst_tool_configuration_name" xml:space="preserve">
    <value>Konfiguration</value>
  </data>
  <data name="tst_tool_configuration_description" xml:space="preserve">
    <value>Verwaltung von Einstellungen und Konfiguration</value>
  </data>
  <data name="tst_tool_commerce_name" xml:space="preserve">
    <value>Handelsverwaltung</value>
  </data>
  <data name="tst_tool_commerce_description" xml:space="preserve">
    <value>Werkzeuge für die Handelsverwaltung</value>
  </data>
  <data name="tst_tool_roles_name" xml:space="preserve">
    <value> Rollenverwaltung</value>
  </data>
  <data name="tst_tool_roles_description" xml:space="preserve">
    <value>Verwaltung der Rollen</value>
  </data>
  <data name="tst_tool_partners_name" xml:space="preserve">
    <value>Partnerverwaltung</value>
  </data>
  <data name="tst_tool_partners_description" xml:space="preserve">
    <value>Partnerverwaltung</value>
  </data>
  <data name="tst_tool_traductions_name" xml:space="preserve">
    <value> Übersetzungen</value>
  </data>
  <data name="tst_tool_traductions_description" xml:space="preserve">
    <value>Übersetzungsverwaltung</value>
  </data>
  <data name="tst_tool_monitoring_name" xml:space="preserve">
    <value>Überwachung</value>
  </data>
  <data name="tst_tool_monitoring_description" xml:space="preserve">
    <value>Überwachung und Nachverfolgung</value>
  </data>
  <data name="tst_tool_securite_name" xml:space="preserve">
    <value>Sicherheit</value>
  </data>
  <data name="tst_tool_securite_description" xml:space="preserve">
    <value>Sicherheitsverwaltung</value>
  </data>
  <data name="tst_tool_outils_dev_name" xml:space="preserve">
    <value>Entwicklungstools</value>
  </data>
  <data name="tst_tool_outils_dev_description" xml:space="preserve">
    <value>Entwicklungs- und Debugging-Tools</value>
  </data>
  <data name="common_voir_modules_label" xml:space="preserve">
    <value> Module anzeigen</value>
  </data>
  <data name="common_survolez_outils_pour_voir_modules" xml:space="preserve">
    <value>Fahren Sie mit der Maus über die Tools, um die verfügbaren Module zu sehen</value>
  </data>
  <data name="loading_spinner_visually_hidden_text" xml:space="preserve">
    <value>Wird geladen...</value>
  </data>
  <data name="loading_spinner_message_text" xml:space="preserve">
    <value>Daten werden geladen...</value>
  </data>
  <data name="pagination_items_per_page_label" xml:space="preserve">
    <value>Elemente pro Seite</value>
  </data>
  <data name="pagination_showing_items_range" xml:space="preserve">
    <value>Zeige Elemente {0} bis {1} von insgesamt {2}</value>
  </data>
  <data name="pagination_navigation_label" xml:space="preserve">
    <value>Seitennavigation</value>
  </data>
  <data name="pagination_button_previous" xml:space="preserve">
    <value>Zurück</value>
  </data>
  <data name="pagination_button_next" xml:space="preserve">
    <value> Weiter</value>
  </data>
  <data name="pagination_page_info_text" xml:space="preserve">
    <value>Seite {0} von {1}</value>
  </data>
  <data name="toast_message_data_loaded_success" xml:space="preserve">
    <value>Daten wurden erfolgreich geladen</value>
  </data>
  <data name="widget-cross-selling" xml:space="preserve">
    <value>Cross-Selling</value>
  </data>
  <data name="widget-waiting-list" xml:space="preserve">
    <value>Wartelisten-Konfig</value>
  </data>
  <data name="widget-catalogue-offre" xml:space="preserve">
    <value>Angebotskataloge</value>
  </data>
  <data name="liste_catalogues_offres" xml:space="preserve">
    <value>Angebotskataloge</value>
  </data>
  <data name="configuration_listes_attente" xml:space="preserve">
    <value> Wartelistenkonfiguration</value>
  </data>
  <data name="gestion_widgets" xml:space="preserve">
    <value> Widget-Verwaltung</value>
  </data>
  <data name="description_gestion_widgets" xml:space="preserve">
    <value>Verwalten der Anzeige und Reihenfolge der Widgets.</value>
  </data>
  <data name=" liste-structure-with-partenaires" xml:space="preserve">
    <value>Liste der Strukturen und ihrer Partner</value>
  </data>
  <data name="structure_partners_title" xml:space="preserve">
    <value>Partner der Struktur</value>
  </data>
  <data name="selected_structures_partners_title" xml:space="preserve">
    <value>Ausgewählte Strukturen &amp; Partner</value>
  </data>
  <data name="structures_partners_title" xml:space="preserve">
    <value>Strukturen &amp; Partner</value>
  </data>
  <data name="structure_associated_partners" xml:space="preserve">
    <value>Mit dieser Struktur verbundene Partner</value>
  </data>
  <data name="selected_structures_count" xml:space="preserve">
    <value>Struktur(en) ausgewählt</value>
  </data>
  <data name="from_partners_page" xml:space="preserve">
    <value>Von der Partnerseite</value>
  </data>
  <data name="structures_partners_management" xml:space="preserve">
    <value>Verwaltung der Beziehungen zwischen Strukturen und Partnern</value>
  </data>
  <data name="return_to_partners" xml:space="preserve">
    <value>Zurück zu Partnern</value>
  </data>
  <data name="full_list" xml:space="preserve">
    <value>Vollständige Liste</value>
  </data>
  <data name="structures_found" xml:space="preserve">
    <value>Strukturen gefunden</value>
  </data>
  <data name="total_partners" xml:space="preserve">
    <value>Partner insgesamt</value>
  </data>
  <data name="with_partners" xml:space="preserve">
    <value>Mit Partnern</value>
  </data>
  <data name="loading_data" xml:space="preserve">
    <value>Daten werden geladen...</value>
  </data>
  <data name="loading_error" xml:space="preserve">
    <value>Fehler beim Laden:</value>
  </data>
  <data name="no_structures_found" xml:space="preserve">
    <value>Keine Strukturen gefunden</value>
  </data>
  <data name="search_structures" xml:space="preserve">
    <value>Strukturen suchen...</value>
  </data>
  <data name="liaison_revendeur_title" xml:space="preserve">
    <value>Wiederverkäufer-Käuferprofil-Verknüpfung</value>
  </data>
  <data name="liaison_revendeur_description" xml:space="preserve">
    <value>Links zwischen Wiederverkäufern und Käuferprofilen auf Strukturen erstellen</value>
  </data>
  <data name="step_1_select_revendeur" xml:space="preserve">
    <value>Einen Wiederverkäufer auswählen</value>
  </data>
  <data name="select_profil_acheteur" xml:space="preserve">
    <value> Wählen Sie ein Käuferprofil aus</value>
  </data>
  <data name="step_3_select_structure" xml:space="preserve">
    <value>Eine Struktur auswählen</value>
  </data>
  <data name="select" xml:space="preserve">
    <value>Auswählen</value>
  </data>
  <data name="no_revendeurs_found" xml:space="preserve">
    <value>Keine Wiederverkäufer gefunden</value>
  </data>
  <data name="selected_revendeur" xml:space="preserve">
    <value>Ausgewählter Wiederverkäufer</value>
  </data>
  <data name="no_profils_lies_found" xml:space="preserve">
    <value>Keine verknüpften Profile gefunden</value>
  </data>
  <data name="show_all_profils" xml:space="preserve">
    <value>Alle Profile anzeigen</value>
  </data>
  <data name="selected_profil" xml:space="preserve">
    <value>Ausgewähltes Profil</value>
  </data>
  <data name="save_liaison" xml:space="preserve">
    <value>Link speichern</value>
  </data>
  <data name="liaison_saved_successfully" xml:space="preserve">
    <value>Link erfolgreich gespeichert!</value>
  </data>
  <data name="liaison_details" xml:space="preserve">
    <value>Link-Details</value>
  </data>
  <data name="revendeur" xml:space="preserve">
    <value>Wiederverkäufer</value>
  </data>
  <data name="profil_acheteur" xml:space="preserve">
    <value>Käuferprofil</value>
  </data>
  <data name="create_new_liaison" xml:space="preserve">
    <value>Neuen Link erstellen</value>
  </data>
  <data name="check_database_connection" xml:space="preserve">
    <value>Überprüfen Sie die Datenbankverbindung der Struktur</value>
  </data>
  <data name="database_connection_issue" xml:space="preserve">
    <value>Datenbankverbindungsproblem</value>
  </data>
  <data name="structure_database_not_configured" xml:space="preserve">
    <value>Struktur {{0}} hat keine konfigurierte oder zugängliche Datenbankverbindung.</value>
  </data>
  <data name="liaison-acheteurr-revendeur" xml:space="preserve">
    <value>händler verknüpfen - käuferprofil</value>
  </data>
  <data name="select_target_structure_explanation" xml:space="preserve">
    <value> Bitte wählen Sie die Struktur aus</value>
  </data>
  <data name="liaison_revendeur_description_new_workflow" xml:space="preserve">
    <value> Beschreibung des neuen Workflows zur Verknüpfung von Wiederverkäufern</value>
  </data>
  <data name="choose_profil_to_add" xml:space="preserve">
    <value>Wählen Sie ein Profil zum Hinzufügen</value>
  </data>
  <data name="select_structure_profil_to_link" xml:space="preserve">
    <value>Wählen Sie das Strukturprofil zum Verknüpfen aus</value>
  </data>
  <data name="search_and_select_profil" xml:space="preserve">
    <value>Profil suchen und auswählen</value>
  </data>
  <data name="select_revendeur_description" xml:space="preserve">
    <value>Beschreibung der Wiederverkäuferauswahl</value>
  </data>
  <data name="step_2_profils_lies_revendeur" xml:space="preserve">
    <value>Mit dem Wiederverkäufer verknüpfte Profile</value>
  </data>
  <data name="no_profils_lies_workflow_explanation" xml:space="preserve">
    <value>Keine verknüpften Profile im Workflow gefunden.</value>
  </data>
  <data name="create_liaison_first_message" xml:space="preserve">
    <value>Bitte erstellen Sie zuerst eine Verbindung.</value>
  </data>
  <data name="back_to_revendeurs" xml:space="preserve">
    <value> Zurück zu den Wiederverkäufern</value>
  </data>
  <data name="link_revendeur_to_profil_acheteur" xml:space="preserve">
    <value> Den Wiederverkäufer mit dem Käuferprofil verknüpfen</value>
  </data>
  <data name="selected_profils_for_liaison" xml:space="preserve">
    <value>Ausgewählte Profile für Verbindung</value>
  </data>
  <data name="confirmation" xml:space="preserve">
    <value>Bestätigung</value>
  </data>
  <data name="loading" xml:space="preserve">
    <value>Laden</value>
  </data>
  <data name="no_revendeurs_database_issue" xml:space="preserve">
    <value>Wiederverkäufer können nicht aus der Datenbank geladen werden.</value>
  </data>
  <data name="revendeur_from_structure" xml:space="preserve">
    <value>Wiederverkäufer aus Struktur</value>
  </data>
  <data name="back" xml:space="preserve">
    <value>Zurück</value>
  </data>
  <data name="loading_profiles" xml:space="preserve">
    <value>Profile laden</value>
  </data>
  <data name="link_all_button" xml:space="preserve">
    <value>Alle Profile verknüpfen</value>
  </data>
  <data name="revendeur_profil_acheteur" xml:space="preserve">
    <value>Wiederverkäufer-Käuferprofil</value>
  </data>
  <data name="revendeur_structure" xml:space="preserve">
    <value>Wiederverkäufer-Struktur</value>
  </data>
  <data name="structure_profil_acheteur" xml:space="preserve">
    <value>Struktur-Käuferprofil</value>
  </data>
  <data name="error_loading_initial" xml:space="preserve">
    <value>Fehler beim ersten Laden: {0}</value>
  </data>
  <data name="error_selecting_revendeur" xml:space="preserve">
    <value>Fehler bei der Auswahl des Wiederverkäufers: {0}</value>
  </data>
  <data name="error_loading_structures" xml:space="preserve">
    <value>Fehler beim Laden der Strukturen: {0}</value>
  </data>
  <data name="error_please_select_structure" xml:space="preserve">
    <value>Bitte wählen Sie eine Struktur aus.</value>
  </data>
  <data name="error_invalid_structure_id" xml:space="preserve">
    <value>Ungültige Struktur-ID: {0}</value>
  </data>
  <data name="error_loading_structure_profiles" xml:space="preserve">
    <value>Fehler beim Laden der Strukturprofile: {0}</value>
  </data>
  <data name="error_please_select_profile" xml:space="preserve">
    <value>Bitte wählen Sie mindestens ein Käuferprofil zum Verknüpfen aus.</value>
  </data>
  <data name="error_saving_liaison" xml:space="preserve">
    <value>Fehler beim Speichern: {0}</value>
  </data>
  <data name="export_coupons_actifs_periode" xml:space="preserve">
    <value>Aktive Gutscheine nach Zeitraum exportieren</value>
  </data>
  <data name="export_pour_profil" xml:space="preserve">
    <value>Export für Profil:</value>
  </data>
  <data name="exporter_seulement_coupons_actifs" xml:space="preserve">
    <value>Nur aktive Gutscheine exportieren (nicht abgelaufen und nicht verwendet)</value>
  </data>
  <data name="aucun_coupon_periode_selectionnee" xml:space="preserve">
    <value>Keine Gutscheine für den ausgewählten Zeitraum gefunden</value>
  </data>
  <data name="coupon_export_success_avec_filtre" xml:space="preserve">
    <value>{0} Gutschein(e) erfolgreich für {1} exportiert</value>
  </data>
  <data name="select_revendeur" xml:space="preserve">
    <value>Einen Händler auswählen</value>
  </data>
  <data name="filter_by_status" xml:space="preserve">
    <value>Nach Status filtern</value>
  </data>
  <data name="all_coupons" xml:space="preserve">
    <value>Alle Gutscheine</value>
  </data>
  <data name="active_coupons_only" xml:space="preserve">
    <value>Nur aktive Gutscheine</value>
  </data>
  <data name="expired_coupons_only" xml:space="preserve">
    <value>Nur abgelaufene Gutscheine</value>
  </data>
  <data name="used_coupons_only" xml:space="preserve">
    <value>Nur verwendete Gutscheine</value>
  </data>
  <data name="operation_date" xml:space="preserve">
    <value>Vorgangsdatum</value>
  </data>
  <data name="select_structure_to_view_partners" xml:space="preserve">
    <value>Wählen Sie eine Struktur aus, um ihre zugehörigen Partner anzuzeigen</value>
  </data>
  <data name="search_partners_in_structure" xml:space="preserve">
    <value>Partner in dieser Struktur suchen</value>
  </data>
  <data name="search_partner_placeholder" xml:space="preserve">
    <value>Partnername eingeben...</value>
  </data>
  <data name="partners_of_structure" xml:space="preserve">
    <value>Partner der Struktur</value>
  </data>
  <data name="partners_found" xml:space="preserve">
    <value>Partner gefunden</value>
  </data>
  <data name="no_partners_in_structure" xml:space="preserve">
    <value>Keine Partner für diese Struktur gefunden</value>
  </data>
</root>