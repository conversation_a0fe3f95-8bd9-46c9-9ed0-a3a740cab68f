using Core.Themis.Libraries.DTO.Enums.ThemisSupportTools;
using System;


namespace Core.Themis.Libraries.DTO.WSAdmin
{
    /// <summary>
    /// C'est la structure provenant de WSADMIN
    /// </summary>
    public class WsAdminStructureDTO : IEquatable<WsAdminStructureDTO>
    {
        public string StructureId { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public char? Supp { get; set; }
        public bool IsDeleted { get; set; }
        public DateTime? DateDeleted { get; set; }
        public bool ExistInTEST { get; set; }
        public bool ExistInPROD { get; set; }

        // Propriétés ajoutées pour le module ServiceInclusionExclusion
        public bool IsInInclusionPreProd { get; set; }
        public bool IsInExclusionPreProd { get; set; }
        public bool IsInInclusionProd { get; set; }
        public bool IsInExclusionProd { get; set; }

        // Propriétés dérivées pour l'état
        public bool IsProd => !IsInExclusionProd && !IsInInclusionPreProd;
        public bool IsPreprod => IsInInclusionPreProd;
        public bool IsBloque => IsInExclusionProd && !IsInInclusionPreProd;

        public bool Equals(WsAdminStructureDTO other)
        {
            if (other is null)
            {
                return false;
            }
            return this.StructureId == other.StructureId && this.Name == other.Name;
        }

        public override bool Equals(object obj)
        {
            return Equals(obj as WsAdminStructureDTO);
        }

        public override int GetHashCode()
        {
            return (StructureId, Name).GetHashCode();
        }

        public ServiceType ServiceType { get; set; }

        /// <summary>
        /// Date d'opération de l'association partenaire-structure
        /// </summary>
        public DateTime? DateOperation { get; set; }

    }

    public enum ServiceType
    {
        CreateCmd,
        Edition,
        Paiement,
        EnvoiMail,
        EnvoiMailInfo
    }

}
