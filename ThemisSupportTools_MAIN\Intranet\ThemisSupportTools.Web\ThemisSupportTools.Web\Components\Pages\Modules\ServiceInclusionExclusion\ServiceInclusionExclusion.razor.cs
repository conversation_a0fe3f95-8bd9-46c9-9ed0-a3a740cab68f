﻿using Microsoft.AspNetCore.Components;
using static Core.Themis.Libraries.BLL.WsAdmin.WsAdminStructuresManager;

namespace ThemisSupportTools.Web.Components.Pages.Modules.ServiceInclusionExclusion
{
    public partial class ServiceInclusionExclusion
    {
        const string MODULE_NAME = "service-inclusion-exclusion";

        [Inject]
        private IThemisSupportToolsManager ThemisSupportToolsManager { get; set; } = default!;

        [Inject]
        private ITstAccessService TstAccessService { get; set; } = default!;

        [Inject]
        private IWsAdminStructuresManager WsAdminStructuresManager { get; set; } = default!;

        [Inject]
        private NavigationManager NavigationManager { get; set; } = default!;

        [Inject]
        private IStringLocalizer<Resource> Localizer { get; set; } = default!;

        [Inject]
        private IConfiguration Configuration { get; set; } = default!;

        private bool isLoading = true;
        private bool isSaving = false;
        private TstRoleDTO? TstRole;

        private string searchTerm = "";

        private List<WsAdminStructureDTO> availableStructures = new();
        private WsAdminStructureDTO? selectedStructure = null;

        private Dictionary<ServiceType, List<string>> inclusionsPreProd = new();
        private Dictionary<ServiceType, List<string>> exclusionsPreProd = new();
        private Dictionary<ServiceType, List<string>> inclusionsProd = new();
        private Dictionary<ServiceType, List<string>> exclusionsProd = new();

        private ServiceType activeService = ServiceType.CreateCmd;
        private List<ServiceType> serviceOrder = new List<ServiceType>
        {
            ServiceType.CreateCmd,
            ServiceType.Paiement,
            ServiceType.Edition,
            ServiceType.EnvoiMail,
            ServiceType.EnvoiMailInfo
        };

        List<ToastMessage> toastMessages = new();
        ToastsPlacement toastsPlacement = ToastsPlacement.TopRight;

        protected override async Task OnInitializedAsync()
        {
            if (!TstAccessService.IsGranted(MODULE_NAME))
            {
                NavigationManager.NavigateTo("");
                return;
            }

            try
            {
                isLoading = true;
                StateHasChanged();

                foreach (ServiceType service in Enum.GetValues(typeof(ServiceType)))
                {
                    inclusionsPreProd[service] = new List<string>();
                    exclusionsPreProd[service] = new List<string>();
                    inclusionsProd[service] = new List<string>();
                    exclusionsProd[service] = new List<string>();
                }

                await LoadAvailableStructures();

                foreach (ServiceType service in Enum.GetValues(typeof(ServiceType)))
                {
                    await LoadXmlFilesForService(service);
                }

                UpdateStructureStatus();
            }
            catch (Exception ex)
            {
                ShowToast(ToastType.Danger, Localizer["common_error"], Localizer["erreur_chargement_donnees", ex.Message]);
            }
            finally
            {
                isLoading = false;
                StateHasChanged();
            }
        }

        // Méthode pour obtenir les structures filtrées
        private List<WsAdminStructureDTO> GetFilteredStructures()
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
            {
                return availableStructures;
            }

            string searchTermLower = searchTerm.ToLower();
            return availableStructures
                .Where(s => s.StructureId.ToLower().Contains(searchTermLower) ||
                            s.Name.ToLower().Contains(searchTermLower))
                .ToList();
        }

        // Méthode pour obtenir les services dans l'ordre personnalisé
        private List<ServiceType> GetOrderedServices()
        {
            return serviceOrder;
        }

        private void SearchStructures()
        {
            StateHasChanged();
        }

        private void SelectStructure(WsAdminStructureDTO structure)
        {
            selectedStructure = structure;
            StateHasChanged();
        }

        // Méthode pour vérifier si une structure est incluse/exclue dans un service spécifique
        private string GetStructureStatus(ServiceType service, bool isProd, string structureId)
        {
            if (isProd)
            {
                if (inclusionsProd[service].Contains(structureId))
                    return Localizer["structure_included"];
                else if (exclusionsProd[service].Contains(structureId))
                    return Localizer["structure_excluded"];
                else
                    return Localizer["structure_default_status"];
            }
            else
            {
                if (inclusionsPreProd[service].Contains(structureId))
                    return Localizer["structure_included"];
                else if (exclusionsPreProd[service].Contains(structureId))
                    return Localizer["structure_excluded"];
                else
                    return Localizer["structure_default_status"];
            }
        }

        // Méthode pour vérifier si une structure est bloquée pour un service spécifique
        private bool IsStructureBlocked(ServiceType service, string structureId)
        {
            // Une structure est considérée comme bloquée si elle est exclue en PROD
            // et non incluse en PREPROD
            return exclusionsProd[service].Contains(structureId) &&
                   !inclusionsPreProd[service].Contains(structureId);
        }

        private async Task LoadAvailableStructures()
        {
            try
            {
                availableStructures = (await WsAdminStructuresManager.GetWsAdminStructuresGlobalesAsync(false))
                    .Where(s => !s.IsDeleted)
                    .OrderBy(s => s.StructureId)
                    .ToList();

                if (availableStructures.Count == 0)
                {
                    ShowToast(ToastType.Warning, Localizer["common_warning"], Localizer["structure_no_data_found"]);
                }
            }
            catch (Exception ex)
            {
                ShowToast(ToastType.Danger, Localizer["common_error"], string.Format(Localizer["structure_loading_error"], ex.Message));

                try
                {
                    int currentStructureId = TstAccessService.StructureId;
                    string currentStructureName = TstAccessService.StructureName;

                    if (currentStructureId > 0)
                    {
                        availableStructures.Add(new WsAdminStructureDTO
                        {
                            StructureId = currentStructureId.ToString("0000"),
                            Name = currentStructureName
                        });
                    }
                }
                catch (Exception innerEx)
                {
                    Console.WriteLine($"Erreur lors de la récupération de la structure actuelle: {innerEx.Message}");
                }
            }
        }

        private void UpdateStructureStatus()
        {
            foreach (var structure in availableStructures)
            {
                // Mise à jour des drapeaux pour chaque service
                structure.IsInInclusionPreProd = inclusionsPreProd[activeService].Contains(structure.StructureId);
                structure.IsInExclusionPreProd = exclusionsPreProd[activeService].Contains(structure.StructureId);
                structure.IsInInclusionProd = inclusionsProd[activeService].Contains(structure.StructureId);
                structure.IsInExclusionProd = exclusionsProd[activeService].Contains(structure.StructureId);
            }
        }

        private async Task LoadXmlFilesForService(ServiceType service)
        {
            try
            {
                string preprodPath = Configuration["ServiceRattrapePaths:PreProdPath"];
                string prodPath = Configuration["ServiceRattrapePaths:ProdPath"];

                // Utiliser le manager pour charger les fichiers XML
                string preprodServicePath = Path.Combine(preprodPath, GetServicePath(service));
                string prodServicePath = Path.Combine(prodPath, GetServicePath(service));

                inclusionsPreProd[service] = await WsAdminStructuresManager.LoadStructuresFromXmlAsync(
                    Path.Combine(preprodServicePath, "inclusionsStructures.xml"));
                exclusionsPreProd[service] = await WsAdminStructuresManager.LoadStructuresFromXmlAsync(
                    Path.Combine(preprodServicePath, "exclusionsStructures.xml"));
                inclusionsProd[service] = await WsAdminStructuresManager.LoadStructuresFromXmlAsync(
                    Path.Combine(prodServicePath, "inclusionsStructures.xml"));
                exclusionsProd[service] = await WsAdminStructuresManager.LoadStructuresFromXmlAsync(
                    Path.Combine(prodServicePath, "exclusionsStructures.xml"));
            }
            catch (Exception ex)
            {
                string errorMsg = string.Format(Localizer["xml_loading_error_for_service"], service, ex.Message);
                throw new Exception(errorMsg);
            }
        }

        private string GetServicePath(ServiceType serviceType)
        {
            return serviceType switch
            {
                ServiceType.CreateCmd => "createCmd",
                ServiceType.Edition => "doEdition",
                ServiceType.Paiement => "doPaiement",
                ServiceType.EnvoiMail => "envoiemail",
                ServiceType.EnvoiMailInfo => "envoiemailInfo",
                _ => throw new NotImplementedException()
            };
        }

        private string GetServiceDisplayName(ServiceType serviceType)
        {
            return serviceType switch
            {
                ServiceType.CreateCmd => Localizer["creation_commande"],
                ServiceType.Edition => Localizer["edition_commande"],
                ServiceType.Paiement => Localizer["paiement_commande"],
                ServiceType.EnvoiMail => Localizer["envoi_mail"],
                ServiceType.EnvoiMailInfo => Localizer["envoi_mail_info"],
                _ => serviceType.ToString()
            };
        }

        private void ChangeActiveService(ServiceType serviceType)
        {
            activeService = serviceType;
            UpdateStructureStatus();
            StateHasChanged();
        }

        private async Task ToggleProd(string structureId, bool isChecked)
        {
            try
            {
                isSaving = true;
                StateHasChanged();

                var structure = availableStructures.FirstOrDefault(s => s.StructureId == structureId);
                if (structure == null) return;

                // Sélectionner automatiquement la structure pour afficher le récapitulatif
                selectedStructure = structure;

                string preprodPath = Configuration["ServiceRattrapePaths:PreProdPath"];
                string prodPath = Configuration["ServiceRattrapePaths:ProdPath"];

                if (isChecked)
                {
                    // Activer en PROD:
                    // Supprimer de exclusionsStructures.xml PROD
                    await WsAdminStructuresManager.RemoveStructureAsync(activeService, false, false, structureId, prodPath);
                    ShowToast(ToastType.Success, Localizer["common_success"],
                        Localizer["structure_removed_from_exclusion_prod", structureId]);

                    // Supprimer de inclusionsStructures.xml PREPROD
                    await WsAdminStructuresManager.RemoveStructureAsync(activeService, true, true, structureId, preprodPath);
                    ShowToast(ToastType.Success, Localizer["common_success"],
                        Localizer["structure_removed_from_inclusion_preprod", structureId]);
                }
                else
                {
                    // Désactiver en PROD = Ajouter dans exclusionsStructures.xml PROD
                    await WsAdminStructuresManager.SaveStructureConfigurationAsync(activeService, false, false, structureId, prodPath);
                    ShowToast(ToastType.Success, Localizer["common_success"],
                        Localizer["structure_added_to_exclusion_prod", structureId]);
                }
            }
            catch (Exception ex)
            {
                ShowToast(ToastType.Danger, Localizer["common_error"], Localizer["error_generic", ex.Message]);
            }
            finally
            {
                isSaving = false;
                // Rafraîchir les statuts des structures
                await LoadXmlFilesForService(activeService);
                UpdateStructureStatus();
                StateHasChanged();
            }
        }

        private async Task TogglePreprod(string structureId, bool isChecked)
        {
            try
            {
                isSaving = true;
                StateHasChanged();

                var structure = availableStructures.FirstOrDefault(s => s.StructureId == structureId);
                if (structure == null) return;

                // Sélectionner automatiquement la structure pour afficher le récapitulatif
                selectedStructure = structure;

                string preprodPath = Configuration["ServiceRattrapePaths:PreProdPath"];
                string prodPath = Configuration["ServiceRattrapePaths:ProdPath"];

                if (isChecked)
                {
                    // Activer en PREPROD:
                    // Ajouter dans exclusionsStructures.xml PROD
                    await WsAdminStructuresManager.SaveStructureConfigurationAsync(activeService, false, false, structureId, prodPath);
                    ShowToast(ToastType.Success, Localizer["common_success"],
                        Localizer["structure_added_to_exclusion_prod", structureId]);

                    // Ajouter dans inclusionsStructures.xml PREPROD
                    await WsAdminStructuresManager.SaveStructureConfigurationAsync(activeService, true, true, structureId, preprodPath);
                    ShowToast(ToastType.Success, Localizer["common_success"],
                        Localizer["structure_added_to_inclusion_preprod", structureId]);
                }
                else
                {
                    // Désactiver en PREPROD = Supprimer de inclusionsStructures.xml PREPROD
                    await WsAdminStructuresManager.RemoveStructureAsync(activeService, true, true, structureId, preprodPath);
                    ShowToast(ToastType.Success, Localizer["common_success"],
                        Localizer["structure_removed_from_inclusion_preprod", structureId]);
                }
            }
            catch (Exception ex)
            {
                ShowToast(ToastType.Danger, Localizer["common_error"], Localizer["error_generic", ex.Message]);
            }
            finally
            {
                isSaving = false;
                // Rafraîchir les statuts des structures
                await LoadXmlFilesForService(activeService);
                UpdateStructureStatus();
                StateHasChanged();
            }
        }
        private async Task ToggleBloque(string structureId, bool isChecked)
        {
            try
            {
                isSaving = true;
                StateHasChanged();

                var structure = availableStructures.FirstOrDefault(s => s.StructureId == structureId);
                if (structure == null) return;

                // Sélectionner automatiquement la structure pour afficher le récapitulatif
                selectedStructure = structure;

                string preprodPath = Configuration["ServiceRattrapePaths:PreProdPath"];
                string prodPath = Configuration["ServiceRattrapePaths:ProdPath"];

                if (isChecked)
                {
                    // Bloquer:
                    //Ajouter dans exclusionsStructures.xml PROD
                    await WsAdminStructuresManager.SaveStructureConfigurationAsync(activeService, false, false, structureId, prodPath);
                    ShowToast(ToastType.Success, Localizer["common_success"],
                        Localizer["structure_added_to_exclusion_prod", structureId]);

                    // Supprimer de inclusionsStructures.xml PREPROD
                    await WsAdminStructuresManager.RemoveStructureAsync(activeService, true, true, structureId, preprodPath);
                    ShowToast(ToastType.Success, Localizer["common_success"],
                        Localizer["structure_removed_from_inclusion_preprod", structureId]);
                }
                else
                {
                    // Débloquer = Supprimer de exclusionsStructures.xml PROD
                    await WsAdminStructuresManager.RemoveStructureAsync(activeService, false, false, structureId, prodPath);
                    ShowToast(ToastType.Success, Localizer["common_success"],
                        Localizer["structure_removed_from_exclusion_prod", structureId]);
                }
            }
            catch (Exception ex)
            {
                ShowToast(ToastType.Danger, Localizer["common_error"], Localizer["error_generic", ex.Message]);
            }
            finally
            {
                isSaving = false;
                // Rafraîchir les statuts des structures
                await LoadXmlFilesForService(activeService);
                UpdateStructureStatus();
                StateHasChanged();
            }
        }

        private void ShowToast(ToastType type, string title, string message)
        {
            toastMessages.Add(new ToastMessage
            {
                Type = type,
                Title = title,
                Message = message,
                AutoHide = true
            });
            StateHasChanged();
        }
    }
}