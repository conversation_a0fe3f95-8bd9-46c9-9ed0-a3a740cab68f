@page "/service-inclusion-exclusion"
@using static Core.Themis.Libraries.BLL.WsAdmin.WsAdminStructuresManager

<PageTitle>@Localizer["services_inclusion_exclusion"]</PageTitle>

@if (isLoading)
{
    <div class="d-flex justify-content-center my-5">
        <div class="spinner-border text-success" role="status">
            <span class="visually-hidden">@Localizer["loading_spinner_span"]</span>
        </div>
    </div>
}
else
{
    <!-- Récapitulatif fixe en utilisant uniquement Bootstrap -->
    @if (selectedStructure != null)
    {
        <div class="position-sticky top-0 bg-white shadow-sm mb-3 pt-2" style="z-index: 1000;">
            <div class="card">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">@Localizer["structure_status_recap"]</h5>
                    <button type="button" class="btn-close btn-close-white" aria-label="Close" @onclick="() => selectedStructure = null"></button>
                </div>
                <div class="card-body p-2">
                    <h6 class="mb-2">@Localizer["structure_selected"]: <strong>@selectedStructure.StructureId - @selectedStructure.Name</strong></h6>
                    <div class="table-responsive" style="max-height: 250px; overflow-y: auto;">
                        <table class="table table-bordered table-sm">
                            <thead class="table-light">
                                <tr>
                                    <th>@Localizer["service_inclusion_exclusion_name"]</th>
                                    <th class="text-center">@Localizer["prod_environment"]</th>
                                    <th class="text-center">@Localizer["preprod_environment"]</th>
                                    <th class="text-center">@Localizer["service_blocked_status"]</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (ServiceType service in GetOrderedServices())
                                {
                                    bool isBlocked = exclusionsProd[service].Contains(selectedStructure.StructureId) &&
                                                   !inclusionsPreProd[service].Contains(selectedStructure.StructureId);
                                    bool isProdActive = !exclusionsProd[service].Contains(selectedStructure.StructureId);
                                    bool isPreprodActive = inclusionsPreProd[service].Contains(selectedStructure.StructureId);

                                    <tr class="@(isBlocked ? "table-warning" : "")">
                                        <td>
                                            @GetServiceDisplayName(service)
                                        </td>
                                        <td class="text-center">
                                            <div class="form-check d-flex justify-content-center">
                                                <input class="form-check-input" type="checkbox" disabled
                                                       checked="@isProdActive" />
                                            </div>
                                        </td>
                                        <td class="text-center">
                                            <div class="form-check d-flex justify-content-center">
                                                <input class="form-check-input" type="checkbox" disabled
                                                       checked="@isPreprodActive" />
                                            </div>
                                        </td>
                                        <td class="text-center">
                                            <div class="form-check d-flex justify-content-center">
                                                <input class="form-check-input" type="checkbox" disabled
                                                       checked="@isBlocked" />
                                            </div>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    }

    <div class="container-fluid">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title">@Localizer["services_inclusion_exclusion"]</h4>
            </div>
            <div class="card-body">

                <!-- Recherche uniquement -->
                <div class="row mb-4">
                    <div class="col-md-12">
                        <div class="input-group">
                            <input type="text" id="searchInput" class="form-control" placeholder="@Localizer["search_button"]" @bind="searchTerm" @bind:event="oninput" />
                            <button class="btn btn-primary" @onclick="SearchStructures">
                                <i class="bi bi-search"></i> @Localizer["search_button"]
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Navigation pour les services -->
                <div class="mt-4">
                    <div class="d-flex flex-wrap mb-3">
                        <a href="#" class="service-nav-link text-decoration-none @(activeService == ServiceType.CreateCmd ? "active" : "")"
                           @onclick="() => ChangeActiveService(ServiceType.CreateCmd)" @onclick:preventDefault="true">
                            @Localizer["creation_commande"]
                        </a>
                        <a href="#" class="service-nav-link text-decoration-none @(activeService == ServiceType.Paiement ? "active" : "")"
                           @onclick="() => ChangeActiveService(ServiceType.Paiement)" @onclick:preventDefault="true">
                            @Localizer["paiement_commande"]
                        </a>
                        <a href="#" class="service-nav-link text-decoration-none @(activeService == ServiceType.Edition ? "active" : "")"
                           @onclick="() => ChangeActiveService(ServiceType.Edition)" @onclick:preventDefault="true">
                            @Localizer["edition_commande"]
                        </a>
                        <a href="#" class="service-nav-link text-decoration-none @(activeService == ServiceType.EnvoiMail ? "active" : "")"
                           @onclick="() => ChangeActiveService(ServiceType.EnvoiMail)" @onclick:preventDefault="true">
                            @Localizer["envoi_mail"]
                        </a>
                        <a href="#" class="service-nav-link text-decoration-none @(activeService == ServiceType.EnvoiMailInfo ? "active" : "")"
                           @onclick="() => ChangeActiveService(ServiceType.EnvoiMailInfo)" @onclick:preventDefault="true">
                            @Localizer["envoi_mail_info"]
                        </a>
                    </div>

                    <!-- Contenu du service sélectionné - Tableau avec checkboxes -->
                    <div class="mt-3">
                        <div class="@(activeService == ServiceType.CreateCmd ? "d-block" : "d-none")">
                            @if (activeService == ServiceType.CreateCmd)
                            {
                                <div class="table-responsive">
                                    <table class="table table-bordered table-hover">
                                        <thead class="table-light">
                                            <tr>
                                                <th>ID</th>
                                                <th>@Localizer["structure_name"]</th>
                                                <th class="text-center">@Localizer["prod_environment"]</th>
                                                <th class="text-center">@Localizer["preprod_environment"]</th>
                                                <th class="text-center">@Localizer["service_inclusion_exlusion_blocked_status"]</th>
                                                <th class="text-center">@Localizer["actions"]</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach (var structure in GetFilteredStructures())
                                            {
                                                bool isBlocked = exclusionsProd[activeService].Contains(structure.StructureId) &&
                                                                !inclusionsPreProd[activeService].Contains(structure.StructureId);
                                                bool isProdActive = !exclusionsProd[activeService].Contains(structure.StructureId);
                                                bool isPreprodActive = inclusionsPreProd[activeService].Contains(structure.StructureId);

                                                <tr class="@(isBlocked ? "table-warning" : "")">
                                                    <td>@structure.StructureId</td>
                                                    <td>@structure.Name</td>
                                                    <!-- PROD -->
                                                    <td class="text-center">
                                                        <div class="form-check d-flex justify-content-center">
                                                            <input class="form-check-input" type="checkbox"
                                                                   checked="@isProdActive"
                                                                   @onchange="async (e) => await ToggleProd(structure.StructureId, (bool)e.Value)"
                                                                   disabled="@isSaving" />
                                                        </div>
                                                    </td>
                                                    <!-- PREPROD -->
                                                    <td class="text-center">
                                                        <div class="form-check d-flex justify-content-center">
                                                            <input class="form-check-input" type="checkbox"
                                                                   checked="@isPreprodActive"
                                                                   @onchange="async (e) => await TogglePreprod(structure.StructureId, (bool)e.Value)"
                                                                   disabled="@isSaving" />
                                                        </div>
                                                    </td>
                                                    <!-- BLOQUÉ -->
                                                    <td class="text-center">
                                                        <div class="form-check d-flex justify-content-center">
                                                            <input class="form-check-input" type="checkbox"
                                                                   checked="@isBlocked"
                                                                   @onchange="async (e) => await ToggleBloque(structure.StructureId, (bool)e.Value)"
                                                                   disabled="@isSaving" />
                                                        </div>
                                                    </td>
                                                    <!-- ACTIONS -->
                                                    <td class="text-center">
                                                        <button class="btn btn-sm btn-info" @onclick="() => SelectStructure(structure)" title="@Localizer["view_structure_details"]">
                                                            <i class="bi bi-info-circle"></i>
                                                        </button>
                                                    </td>
                                                </tr>
                                            }
                                        </tbody>
                                    </table>
                                </div>
                            }
                        </div>

                        <!-- Autres services avec contenu similaire -->
                        <div class="@(activeService == ServiceType.Paiement ? "d-block" : "d-none")">
                            @if (activeService == ServiceType.Paiement)
                            {
                                <div class="table-responsive">
                                    <table class="table table-bordered table-hover">
                                        <thead class="table-light">
                                            <tr>
                                                <th>ID</th>
                                                <th>@Localizer["structure_name"]</th>
                                                <th class="text-center">@Localizer["prod_environment"]</th>
                                                <th class="text-center">@Localizer["preprod_environment"]</th>
                                                <th class="text-center">@Localizer["service_inclusion_exlusion_blocked_status"]</th>
                                                <th class="text-center">@Localizer["actions"]</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach (var structure in GetFilteredStructures())
                                            {
                                                bool isBlocked = exclusionsProd[activeService].Contains(structure.StructureId) &&
                                                                !inclusionsPreProd[activeService].Contains(structure.StructureId);
                                                bool isProdActive = !exclusionsProd[activeService].Contains(structure.StructureId);
                                                bool isPreprodActive = inclusionsPreProd[activeService].Contains(structure.StructureId);

                                                <tr class="@(isBlocked ? "table-warning" : "")">
                                                    <td>@structure.StructureId</td>
                                                    <td>@structure.Name</td>
                                                    <!-- PROD -->
                                                    <td class="text-center">
                                                        <div class="form-check d-flex justify-content-center">
                                                            <input class="form-check-input" type="checkbox"
                                                                   checked="@isProdActive"
                                                                   @onchange="async (e) => await ToggleProd(structure.StructureId, (bool)e.Value)"
                                                                   disabled="@isSaving" />
                                                        </div>
                                                    </td>
                                                    <!-- PREPROD -->
                                                    <td class="text-center">
                                                        <div class="form-check d-flex justify-content-center">
                                                            <input class="form-check-input" type="checkbox"
                                                                   checked="@isPreprodActive"
                                                                   @onchange="async (e) => await TogglePreprod(structure.StructureId, (bool)e.Value)"
                                                                   disabled="@isSaving" />
                                                        </div>
                                                    </td>
                                                    <!-- BLOQUÉ -->
                                                    <td class="text-center">
                                                        <div class="form-check d-flex justify-content-center">
                                                            <input class="form-check-input" type="checkbox"
                                                                   checked="@isBlocked"
                                                                   @onchange="async (e) => await ToggleBloque(structure.StructureId, (bool)e.Value)"
                                                                   disabled="@isSaving" />
                                                        </div>
                                                    </td>
                                                    <!-- ACTIONS -->
                                                    <td class="text-center">
                                                        <button class="btn btn-sm btn-info" @onclick="() => SelectStructure(structure)" title="@Localizer["view_structure_details"]">
                                                            <i class="bi bi-info-circle"></i>
                                                        </button>
                                                    </td>
                                                </tr>
                                            }
                                        </tbody>
                                    </table>
                                </div>
                            }
                        </div>

                        <div class="@(activeService == ServiceType.Edition ? "d-block" : "d-none")">
                            @if (activeService == ServiceType.Edition)
                            {
                                <div class="table-responsive">
                                    <table class="table table-bordered table-hover">
                                        <thead class="table-light">
                                            <tr>
                                                <th>ID</th>
                                                <th>@Localizer["structure_name"]</th>
                                                <th class="text-center">@Localizer["prod_environment"]</th>
                                                <th class="text-center">@Localizer["preprod_environment"]</th>
                                                <th class="text-center">@Localizer["service_inclusion_exlusion_blocked_status"]</th>
                                                <th class="text-center">@Localizer["actions"]</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach (var structure in GetFilteredStructures())
                                            {
                                                bool isBlocked = exclusionsProd[activeService].Contains(structure.StructureId) &&
                                                                !inclusionsPreProd[activeService].Contains(structure.StructureId);
                                                bool isProdActive = !exclusionsProd[activeService].Contains(structure.StructureId);
                                                bool isPreprodActive = inclusionsPreProd[activeService].Contains(structure.StructureId);

                                                <tr class="@(isBlocked ? "table-warning" : "")">
                                                    <td>@structure.StructureId</td>
                                                    <td>@structure.Name</td>
                                                    <!-- PROD -->
                                                    <td class="text-center">
                                                        <div class="form-check d-flex justify-content-center">
                                                            <input class="form-check-input" type="checkbox"
                                                                   checked="@isProdActive"
                                                                   @onchange="async (e) => await ToggleProd(structure.StructureId, (bool)e.Value)"
                                                                   disabled="@isSaving" />
                                                        </div>
                                                    </td>
                                                    <!-- PREPROD -->
                                                    <td class="text-center">
                                                        <div class="form-check d-flex justify-content-center">
                                                            <input class="form-check-input" type="checkbox"
                                                                   checked="@isPreprodActive"
                                                                   @onchange="async (e) => await TogglePreprod(structure.StructureId, (bool)e.Value)"
                                                                   disabled="@isSaving" />
                                                        </div>
                                                    </td>
                                                    <!-- BLOQUÉ -->
                                                    <td class="text-center">
                                                        <div class="form-check d-flex justify-content-center">
                                                            <input class="form-check-input" type="checkbox"
                                                                   checked="@isBlocked"
                                                                   @onchange="async (e) => await ToggleBloque(structure.StructureId, (bool)e.Value)"
                                                                   disabled="@isSaving" />
                                                        </div>
                                                    </td>
                                                    <!-- ACTIONS -->
                                                    <td class="text-center">
                                                        <button class="btn btn-sm btn-info" @onclick="() => SelectStructure(structure)" title="@Localizer["view_structure_details"]">
                                                            <i class="bi bi-info-circle"></i>
                                                        </button>
                                                    </td>
                                                </tr>
                                            }
                                        </tbody>
                                    </table>
                                </div>
                            }
                        </div>

                        <div class="@(activeService == ServiceType.EnvoiMail ? "d-block" : "d-none")">
                            @if (activeService == ServiceType.EnvoiMail)
                            {
                                <div class="table-responsive">
                                    <table class="table table-bordered table-hover">
                                        <thead class="table-light">
                                            <tr>
                                                <th>ID</th>
                                                <th>@Localizer["structure_name"]</th>
                                                <th class="text-center">@Localizer["prod_environment"]</th>
                                                <th class="text-center">@Localizer["preprod_environment"]</th>
                                                <th class="text-center">@Localizer["service_inclusion_exlusion_blocked_status"]</th>
                                                <th class="text-center">@Localizer["actions"]</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach (var structure in GetFilteredStructures())
                                            {
                                                bool isBlocked = exclusionsProd[activeService].Contains(structure.StructureId) &&
                                                                !inclusionsPreProd[activeService].Contains(structure.StructureId);
                                                bool isProdActive = !exclusionsProd[activeService].Contains(structure.StructureId);
                                                bool isPreprodActive = inclusionsPreProd[activeService].Contains(structure.StructureId);

                                                <tr class="@(isBlocked ? "table-warning" : "")">
                                                    <td>@structure.StructureId</td>
                                                    <td>@structure.Name</td>
                                                    <!-- PROD -->
                                                    <td class="text-center">
                                                        <div class="form-check d-flex justify-content-center">
                                                            <input class="form-check-input" type="checkbox"
                                                                   checked="@isProdActive"
                                                                   @onchange="async (e) => await ToggleProd(structure.StructureId, (bool)e.Value)"
                                                                   disabled="@isSaving" />
                                                        </div>
                                                    </td>
                                                    <!-- PREPROD -->
                                                    <td class="text-center">
                                                        <div class="form-check d-flex justify-content-center">
                                                            <input class="form-check-input" type="checkbox"
                                                                   checked="@isPreprodActive"
                                                                   @onchange="async (e) => await TogglePreprod(structure.StructureId, (bool)e.Value)"
                                                                   disabled="@isSaving" />
                                                        </div>
                                                    </td>
                                                    <!-- BLOQUÉ -->
                                                    <td class="text-center">
                                                        <div class="form-check d-flex justify-content-center">
                                                            <input class="form-check-input" type="checkbox"
                                                                   checked="@isBlocked"
                                                                   @onchange="async (e) => await ToggleBloque(structure.StructureId, (bool)e.Value)"
                                                                   disabled="@isSaving" />
                                                        </div>
                                                    </td>
                                                    <!-- ACTIONS -->
                                                    <td class="text-center">
                                                        <button class="btn btn-sm btn-info" @onclick="() => SelectStructure(structure)" title="@Localizer["view_structure_details"]">
                                                            <i class="bi bi-info-circle"></i>
                                                        </button>
                                                    </td>
                                                </tr>
                                            }
                                        </tbody>
                                    </table>
                                </div>
                            }
                        </div>

                        <div class="@(activeService == ServiceType.EnvoiMailInfo ? "d-block" : "d-none")">
                            @if (activeService == ServiceType.EnvoiMailInfo)
                            {
                                <div class="table-responsive">
                                    <table class="table table-bordered table-hover">
                                        <thead class="table-light">
                                            <tr>
                                                <th>ID</th>
                                                <th>@Localizer["structure_name"]</th>
                                                <th class="text-center">@Localizer["prod_environment"]</th>
                                                <th class="text-center">@Localizer["preprod_environment"]</th>
                                                <th class="text-center">@Localizer["service_inclusion_exlusion_blocked_status"]</th>
                                                <th class="text-center">@Localizer["actions"]</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach (var structure in GetFilteredStructures())
                                            {
                                                <tr class="@(structure.IsBloque ? "table-danger" : (structure.IsPreprod ? "table-warning" : "table-success"))">
                                                    <td>@structure.StructureId</td>
                                                    <td>@structure.Name</td>
                                                    <!-- PROD -->
                                                    <td class="text-center">
                                                        <div class="form-check form-switch d-flex justify-content-center">
                                                            <input class="form-check-input" type="checkbox" role="switch"
                                                                   checked="@structure.IsProd"
                                                                   @onchange="(e) => ToggleProd(structure.StructureId.ToString(), (bool)e.Value)"
                                                                   disabled="@isSaving" />
                                                        </div>
                                                    </td>
                                                    <!-- PREPROD -->
                                                    <td class="text-center">
                                                        <div class="form-check form-switch d-flex justify-content-center">
                                                            <input class="form-check-input" type="checkbox" role="switch"
                                                                   checked="@structure.IsPreprod"
                                                                   @onchange="(e) => TogglePreprod(structure.StructureId.ToString(), (bool)e.Value)"
                                                                   disabled="@isSaving" />
                                                        </div>
                                                    </td>
                                                    <!-- STATUT BLOCAGE -->
                                                    <td class="text-center">
                                                        <div class="form-check form-switch d-flex justify-content-center">
                                                            <input class="form-check-input" type="checkbox" role="switch"
                                                                   checked="@structure.IsBloque"
                                                                   @onchange="(e) => ToggleBloque(structure.StructureId.ToString(), (bool)e.Value)"
                                                                   disabled="@isSaving" />
                                                        </div>
                                                    </td>
                                                    <!-- ACTIONS -->
                                                    <td class="text-center">
                                                        <button class="btn btn-sm btn-info" @onclick="() => SelectStructure(structure)" title="@Localizer["view_structure_details"]">
                                                            <i class="bi bi-info-circle"></i>
                                                        </button>
                                                    </td>
                                                </tr>
                                            }
                                        </tbody>
                                    </table>
                                </div>
                            }
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
}

<Toasts Messages="toastMessages" Placement="toastsPlacement" />

<style>
    .service-nav-link {
        padding: 8px 16px;
        margin-right: 8px;
        border-radius: 4px;
        transition: all 0.2s ease;
        cursor: pointer;
        border: 1px solid transparent;
        display: inline-block;
    }

    .service-nav-link:hover {
        background-color: #f8f9fa;
        text-decoration: none !important;
    }

    .service-nav-link.active {
        background-color: #ffffff;
        border: 1px solid #007bff;
        color: #007bff !important;
        font-weight: 500;
    }

    .service-nav-link:not(.active) {
        color: #6c757d !important;
    }
</style>