﻿//------------------------------------------------------------------------------
// <auto-generated>
//     Ce code a été généré par un outil.
//     Version du runtime :4.0.30319.42000
//
//     Les modifications apportées à ce fichier peuvent provoquer un comportement incorrect et seront perdues si
//     le code est régénéré.
// </auto-generated>
//------------------------------------------------------------------------------

namespace ThemisSupportTools.Web.Resources {
    using System;
    
    
    /// <summary>
    ///   Une classe de ressource fortement typée destinée, entre autres, à la consultation des chaînes localisées.
    /// </summary>
    // Cette classe a été générée automatiquement par la classe StronglyTypedResourceBuilder
    // à l'aide d'un outil, tel que ResGen ou Visual Studio.
    // Pour ajouter ou supprimer un membre, modifiez votre fichier .ResX, puis réexécutez ResGen
    // avec l'option /str ou régénérez votre projet VS.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class Resource {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Resource() {
        }
        
        /// <summary>
        ///   Retourne l'instance ResourceManager mise en cache utilisée par cette classe.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("ThemisSupportTools.Web.Resources.Resource", typeof(Resource).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Remplace la propriété CurrentUICulture du thread actuel pour toutes
        ///   les recherches de ressources à l'aide de cette classe de ressource fortement typée.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à List of structures and their partners.
        /// </summary>
        public static string _liste_structure_with_partenaires {
            get {
                return ResourceManager.GetString(" liste-structure-with-partenaires", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à No structures .
        /// </summary>
        public static string _no_structures {
            get {
                return ResourceManager.GetString(" no_structures", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à À propos.
        /// </summary>
        public static string about {
            get {
                return ResourceManager.GetString("about", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à ID of the out-of-subscription offer available on the subscription platform.
        /// </summary>
        public static string abov2_comment_offreidhorsabo_xml {
            get {
                return ResourceManager.GetString("abov2_comment_offreidhorsabo_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Payment mode ID used for deposit.
        /// </summary>
        public static string acomptes_comment_modespaiement_xml {
            get {
                return ResourceManager.GetString("acomptes_comment_modespaiement_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Active coupons only.
        /// </summary>
        public static string active_coupons_only {
            get {
                return ResourceManager.GetString("active_coupons_only", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Add missing keys.
        /// </summary>
        public static string add_missing_keys {
            get {
                return ResourceManager.GetString("add_missing_keys", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Some keys are missing. Do you want to add them?.
        /// </summary>
        public static string add_missing_keys_description {
            get {
                return ResourceManager.GetString("add_missing_keys_description", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Add missing keys.
        /// </summary>
        public static string add_missing_keys_title {
            get {
                return ResourceManager.GetString("add_missing_keys_title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Add Structure.
        /// </summary>
        public static string add_structure {
            get {
                return ResourceManager.GetString("add_structure", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à  Add Sections.
        /// </summary>
        public static string added_sections {
            get {
                return ResourceManager.GetString("added_sections", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Platform Admin Translation.
        /// </summary>
        public static string admin_platform_translation {
            get {
                return ResourceManager.GetString("admin_platform_translation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Additional sections.
        /// </summary>
        public static string advanced_configuration {
            get {
                return ResourceManager.GetString("advanced_configuration", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Blowfish key for encryption.
        /// </summary>
        public static string aexpta_comment_blowfish_xml {
            get {
                return ResourceManager.GetString("aexpta_comment_blowfish_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à HMAC key for secure transactions.
        /// </summary>
        public static string aexpta_comment_hmac_xml {
            get {
                return ResourceManager.GetString("aexpta_comment_hmac_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Merchant ID.
        /// </summary>
        public static string aexpta_comment_merchantid_xml {
            get {
                return ResourceManager.GetString("aexpta_comment_merchantid_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à URL for the Aexpta payment platform.
        /// </summary>
        public static string aexpta_comment_url_xml {
            get {
                return ResourceManager.GetString("aexpta_comment_url_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à All coupons.
        /// </summary>
        public static string all_coupons {
            get {
                return ResourceManager.GetString("all_coupons", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à AppSettings Platforms.
        /// </summary>
        public static string appsettings_plateforms {
            get {
                return ResourceManager.GetString("appsettings-plateforms", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Copy address.
        /// </summary>
        public static string assurancearteo_comment_copyadresse_xml {
            get {
                return ResourceManager.GetString("assurancearteo_comment_copyadresse_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à  réponses Reply address.
        /// </summary>
        public static string assurancearteo_comment_replyadresse_xml {
            get {
                return ResourceManager.GetString("assurancearteo_comment_replyadresse_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Sender of the email.
        /// </summary>
        public static string assurancearteo_comment_senderadresse_xml {
            get {
                return ResourceManager.GetString("assurancearteo_comment_senderadresse_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à SMTP client IP address.
        /// </summary>
        public static string assurancearteo_comment_smtpclientip_xml {
            get {
                return ResourceManager.GetString("assurancearteo_comment_smtpclientip_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Multi-installment payment mode.
        /// </summary>
        public static string atos64_comment_paiementenxfois_xml {
            get {
                return ResourceManager.GetString("atos64_comment_paiementenxfois_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à No coupons to export for {0}..
        /// </summary>
        public static string aucun_coupon_exporter {
            get {
                return ResourceManager.GetString("aucun_coupon_exporter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à No coupons found for the selected period.
        /// </summary>
        public static string aucun_coupon_periode_selectionnee {
            get {
                return ResourceManager.GetString("aucun_coupon_periode_selectionnee", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Back.
        /// </summary>
        public static string back {
            get {
                return ResourceManager.GetString("back", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Back.
        /// </summary>
        public static string back_button {
            get {
                return ResourceManager.GetString("back_button", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Retour aux revendeurs.
        /// </summary>
        public static string back_to_revendeurs {
            get {
                return ResourceManager.GetString("back_to_revendeurs", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Cache time for detail page.
        /// </summary>
        public static string boutique_comment_delaicachedetail_xml {
            get {
                return ResourceManager.GetString("boutique_comment_delaicachedetail_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Cache time for family page.
        /// </summary>
        public static string boutique_comment_delaicachefamille_xml {
            get {
                return ResourceManager.GetString("boutique_comment_delaicachefamille_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Cache time for home shop page.
        /// </summary>
        public static string boutique_comment_delaicachehome_xml {
            get {
                return ResourceManager.GetString("boutique_comment_delaicachehome_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Cache time for menu.
        /// </summary>
        public static string boutique_comment_delaicachemenu_xml {
            get {
                return ResourceManager.GetString("boutique_comment_delaicachemenu_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Cache time for sub-family page.
        /// </summary>
        public static string boutique_comment_delaicachesousfamille_xml_ {
            get {
                return ResourceManager.GetString("boutique_comment_delaicachesousfamille_xml ", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Add coupons.
        /// </summary>
        public static string button_add_coupons {
            get {
                return ResourceManager.GetString("button_add_coupons", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Cancel.
        /// </summary>
        public static string button_cancel {
            get {
                return ResourceManager.GetString("button_cancel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Close.
        /// </summary>
        public static string button_close {
            get {
                return ResourceManager.GetString("button_close", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Delete.
        /// </summary>
        public static string button_delete {
            get {
                return ResourceManager.GetString("button_delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Edit.
        /// </summary>
        public static string button_edit {
            get {
                return ResourceManager.GetString("button_edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Expert.
        /// </summary>
        public static string button_export {
            get {
                return ResourceManager.GetString("button_export", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Export these coupons.
        /// </summary>
        public static string button_export_these_coupons {
            get {
                return ResourceManager.GetString("button_export_these_coupons", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Exporting....
        /// </summary>
        public static string button_exporting {
            get {
                return ResourceManager.GetString("button_exporting", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Filtrer.
        /// </summary>
        public static string button_filter {
            get {
                return ResourceManager.GetString("button_filter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Generate coupons.
        /// </summary>
        public static string button_generate {
            get {
                return ResourceManager.GetString("button_generate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Generate custom XML file.
        /// </summary>
        public static string button_generate_xml_file_label {
            get {
                return ResourceManager.GetString("button.generate_xml_file.label", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Generating custom XML file....
        /// </summary>
        public static string button_generate_xml_file_loading {
            get {
                return ResourceManager.GetString("button.generate_xml_file.loading", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à New Translation.
        /// </summary>
        public static string button_new_translation {
            get {
                return ResourceManager.GetString("button_new_translation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Sections Translation.
        /// </summary>
        public static string button_sections {
            get {
                return ResourceManager.GetString("button_sections", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Subscription Platform Translations.
        /// </summary>
        public static string button_translations_abo_platform {
            get {
                return ResourceManager.GetString("button_translations_abo_platform", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Customer Area Translations.
        /// </summary>
        public static string button_translations_customer_platform {
            get {
                return ResourceManager.GetString("button_translations_customer_platform", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Translations of the INDIV platform.
        /// </summary>
        public static string button_translations_indiv_platform {
            get {
                return ResourceManager.GetString("button_translations_indiv_platform", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Widget Translation.
        /// </summary>
        public static string button_translations_terms {
            get {
                return ResourceManager.GetString("button_translations_terms", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Validate.
        /// </summary>
        public static string button_validate {
            get {
                return ResourceManager.GetString("button_validate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Variables Translation.
        /// </summary>
        public static string button_variables {
            get {
                return ResourceManager.GetString("button_variables", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à View coupons.
        /// </summary>
        public static string button_view_coupons {
            get {
                return ResourceManager.GetString("button_view_coupons", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à ANNULER.
        /// </summary>
        public static string cancel_button_no {
            get {
                return ResourceManager.GetString("cancel_button_no", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Payment method ID CVCO.
        /// </summary>
        public static string cbmodeid_comment_cvco_xml {
            get {
                return ResourceManager.GetString("cbmodeid_comment_cvco_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Payment method ID E_CARD.
        /// </summary>
        public static string cbmodeid_comment_ecard_xml {
            get {
                return ResourceManager.GetString("cbmodeid_comment_ecard_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Payment method ID CB.
        /// </summary>
        public static string cbmodeid_comment_id_xml {
            get {
                return ResourceManager.GetString("cbmodeid_comment_id_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Payment method ID MasterCard.
        /// </summary>
        public static string cbmodeid_comment_mastercard_xml {
            get {
                return ResourceManager.GetString("cbmodeid_comment_mastercard_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Payment method ID MULTI.
        /// </summary>
        public static string cbmodeid_comment_multi_xml {
            get {
                return ResourceManager.GetString("cbmodeid_comment_multi_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Payment method ID Invoice or Free.
        /// </summary>
        public static string cbmodeid_comment_na_xml {
            get {
                return ResourceManager.GetString("cbmodeid_comment_na_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à User used with LINKAPIACCUSE.
        /// </summary>
        public static string cbmodeid_comment_pa_apilogin_xml {
            get {
                return ResourceManager.GetString("cbmodeid_comment_pa_apilogin_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Password used with LINKAPIACCUSE.
        /// </summary>
        public static string cbmodeid_comment_pa_apipassword_xml {
            get {
                return ResourceManager.GetString("cbmodeid_comment_pa_apipassword_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à If 1, authorization is done then capture after validation, otherwise no authorization.
        /// </summary>
        public static string cbmodeid_comment_pa_autoripuiscapture_true_false_xml {
            get {
                return ResourceManager.GetString("cbmodeid_comment_pa_autoripuiscapture_true_false_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à If O, proceed with an authorization THEN the capture after validation. In this case, the next 2 keys must be present.
        /// </summary>
        public static string cbmodeid_comment_pa_autoripuiscapture_xml {
            get {
                return ResourceManager.GetString("cbmodeid_comment_pa_autoripuiscapture_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Payment method ID CB.
        /// </summary>
        public static string cbmodeid_comment_pa_cb_xml {
            get {
                return ResourceManager.GetString("cbmodeid_comment_pa_cb_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Currency code of the site to be sent to the provider.
        /// </summary>
        public static string cbmodeid_comment_pa_cybermuth1_code_devise_xml {
            get {
                return ResourceManager.GetString("cbmodeid_comment_pa_cybermuth1_code_devise_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à  Site code (provided by CYBERMUTH).
        /// </summary>
        public static string cbmodeid_comment_pa_cybermuth1_code_site_xml {
            get {
                return ResourceManager.GetString("cbmodeid_comment_pa_cybermuth1_code_site_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Hash algorithm used for security.
        /// </summary>
        public static string cbmodeid_comment_pa_cybermuth1_hashalgo_xml {
            get {
                return ResourceManager.GetString("cbmodeid_comment_pa_cybermuth1_hashalgo_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Structure identifier.
        /// </summary>
        public static string cbmodeid_comment_pa_cybermuth1_identifiant_structure_xml {
            get {
                return ResourceManager.GetString("cbmodeid_comment_pa_cybermuth1_identifiant_structure_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Security key for transactions.
        /// </summary>
        public static string cbmodeid_comment_pa_cybermuth1_key_xml {
            get {
                return ResourceManager.GetString("cbmodeid_comment_pa_cybermuth1_key_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Language used for the transaction.
        /// </summary>
        public static string cbmodeid_comment_pa_cybermuth1_langage_xml {
            get {
                return ResourceManager.GetString("cbmodeid_comment_pa_cybermuth1_langage_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Redirect URL to the payment provider.
        /// </summary>
        public static string cbmodeid_comment_pa_cybermuth1_url_redirection_xml {
            get {
                return ResourceManager.GetString("cbmodeid_comment_pa_cybermuth1_url_redirection_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Version of the protocol used.
        /// </summary>
        public static string cbmodeid_comment_pa_cybermuth1_version_xml {
            get {
                return ResourceManager.GetString("cbmodeid_comment_pa_cybermuth1_version_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Payment via CYBERMUTH, specific integration.
        /// </summary>
        public static string cbmodeid_comment_pa_cybermuth1_xml {
            get {
                return ResourceManager.GetString("cbmodeid_comment_pa_cybermuth1_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Payment provider version.
        /// </summary>
        public static string cbmodeid_comment_pa_interfaceversion_xml {
            get {
                return ResourceManager.GetString("cbmodeid_comment_pa_interfaceversion_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Access key (client’s back-office for the provider).
        /// </summary>
        public static string cbmodeid_comment_pa_key_xml {
            get {
                return ResourceManager.GetString("cbmodeid_comment_pa_key_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Link used to request the card capture after successful reception.
        /// </summary>
        public static string cbmodeid_comment_pa_linkapiaccuse_xml {
            get {
                return ResourceManager.GetString("cbmodeid_comment_pa_linkapiaccuse_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Live or TEST Secret Key to be found in the back-office.
        /// </summary>
        public static string cbmodeid_comment_pa_password_xml {
            get {
                return ResourceManager.GetString("cbmodeid_comment_pa_password_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Currency code of the site to be sent to the provider.
        /// </summary>
        public static string cbmodeid_comment_pa_paybox_code_devise_xml {
            get {
                return ResourceManager.GetString("cbmodeid_comment_pa_paybox_code_devise_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Site code (provided by PAYBOX.
        /// </summary>
        public static string cbmodeid_comment_pa_paybox_code_site_xml {
            get {
                return ResourceManager.GetString("cbmodeid_comment_pa_paybox_code_site_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Structure identifier.
        /// </summary>
        public static string cbmodeid_comment_pa_paybox_identifiant_structure_xml {
            get {
                return ResourceManager.GetString("cbmodeid_comment_pa_paybox_identifiant_structure_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Site rank (provided by PAYBOX).
        /// </summary>
        public static string cbmodeid_comment_pa_paybox_rang_site_xml {
            get {
                return ResourceManager.GetString("cbmodeid_comment_pa_paybox_rang_site_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Redirect URL to the payment provider.
        /// </summary>
        public static string cbmodeid_comment_pa_paybox_url_redirection_xml {
            get {
                return ResourceManager.GetString("cbmodeid_comment_pa_paybox_url_redirection_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à payment, two accounts for a provider.
        /// </summary>
        public static string cbmodeid_comment_pa_paybox_xml {
            get {
                return ResourceManager.GetString("cbmodeid_comment_pa_paybox_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Currency code of the site to be sent to the provider.
        /// </summary>
        public static string cbmodeid_comment_pa_paybox2_code_devise_xml {
            get {
                return ResourceManager.GetString("cbmodeid_comment_pa_paybox2_code_devise_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Site code (provided by PAYBOX).
        /// </summary>
        public static string cbmodeid_comment_pa_paybox2_code_site_xml {
            get {
                return ResourceManager.GetString("cbmodeid_comment_pa_paybox2_code_site_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Structure identifier.
        /// </summary>
        public static string cbmodeid_comment_pa_paybox2_identifiant_structure_xml {
            get {
                return ResourceManager.GetString("cbmodeid_comment_pa_paybox2_identifiant_structure_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Site rank (provided by PAYBOX).
        /// </summary>
        public static string cbmodeid_comment_pa_paybox2_rang_site_xml {
            get {
                return ResourceManager.GetString("cbmodeid_comment_pa_paybox2_rang_site_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Redirect URL to the payment provide.
        /// </summary>
        public static string cbmodeid_comment_pa_paybox2_url_redirection_xml {
            get {
                return ResourceManager.GetString("cbmodeid_comment_pa_paybox2_url_redirection_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à payment, two accounts for a provide.
        /// </summary>
        public static string cbmodeid_comment_pa_paybox2_xml {
            get {
                return ResourceManager.GetString("cbmodeid_comment_pa_paybox2_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Currency code of the site to be sent to the provider.
        /// </summary>
        public static string cbmodeid_comment_pa_payboxhmac1_code_devise_xml {
            get {
                return ResourceManager.GetString("cbmodeid_comment_pa_payboxhmac1_code_devise_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Site code (provided by PAYBOXHMAC).
        /// </summary>
        public static string cbmodeid_comment_pa_payboxhmac1_code_site_xml {
            get {
                return ResourceManager.GetString("cbmodeid_comment_pa_payboxhmac1_code_site_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à HMAC key used to secure the transaction.
        /// </summary>
        public static string cbmodeid_comment_pa_payboxhmac1_hmac_xml {
            get {
                return ResourceManager.GetString("cbmodeid_comment_pa_payboxhmac1_hmac_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Structure identifier.
        /// </summary>
        public static string cbmodeid_comment_pa_payboxhmac1_identifiant_structure_xml {
            get {
                return ResourceManager.GetString("cbmodeid_comment_pa_payboxhmac1_identifiant_structure_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Site rank (provided by PAYBOXHMAC).
        /// </summary>
        public static string cbmodeid_comment_pa_payboxhmac1_rang_site_xml {
            get {
                return ResourceManager.GetString("cbmodeid_comment_pa_payboxhmac1_rang_site_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Redirect URL to the payment provider.
        /// </summary>
        public static string cbmodeid_comment_pa_payboxhmac1_url_redirection_xml {
            get {
                return ResourceManager.GetString("cbmodeid_comment_pa_payboxhmac1_url_redirection_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à payment with HMAC, two accounts for a provider.
        /// </summary>
        public static string cbmodeid_comment_pa_payboxhmac1_xml {
            get {
                return ResourceManager.GetString("cbmodeid_comment_pa_payboxhmac1_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Currency code of the site to be sent to the provider.
        /// </summary>
        public static string cbmodeid_comment_pa_payboxhmac2_code_devise_xml {
            get {
                return ResourceManager.GetString("cbmodeid_comment_pa_payboxhmac2_code_devise_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Site code (provided by PAYBOXHMAC).
        /// </summary>
        public static string cbmodeid_comment_pa_payboxhmac2_code_site_xml {
            get {
                return ResourceManager.GetString("cbmodeid_comment_pa_payboxhmac2_code_site_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à HMAC key used to secure the transaction.
        /// </summary>
        public static string cbmodeid_comment_pa_payboxhmac2_hmac_xml {
            get {
                return ResourceManager.GetString("cbmodeid_comment_pa_payboxhmac2_hmac_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Structure identifier.
        /// </summary>
        public static string cbmodeid_comment_pa_payboxhmac2_identifiant_structure_xml {
            get {
                return ResourceManager.GetString("cbmodeid_comment_pa_payboxhmac2_identifiant_structure_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Site rank (provided by PAYBOXHMAC).
        /// </summary>
        public static string cbmodeid_comment_pa_payboxhmac2_rang_site_xml {
            get {
                return ResourceManager.GetString("cbmodeid_comment_pa_payboxhmac2_rang_site_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Acknowledgment URL for the transaction.
        /// </summary>
        public static string cbmodeid_comment_pa_payboxhmac2_url_acquit_xml {
            get {
                return ResourceManager.GetString("cbmodeid_comment_pa_payboxhmac2_url_acquit_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Old redirect URL to the payment provider.
        /// </summary>
        public static string cbmodeid_comment_pa_payboxhmac2_url_redirection_old_xml {
            get {
                return ResourceManager.GetString("cbmodeid_comment_pa_payboxhmac2_url_redirection_old_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Redirect URL to the payment provider.
        /// </summary>
        public static string cbmodeid_comment_pa_payboxhmac2_url_redirection_xml {
            get {
                return ResourceManager.GetString("cbmodeid_comment_pa_payboxhmac2_url_redirection_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à payment with HMAC, two accounts for a provider.
        /// </summary>
        public static string cbmodeid_comment_pa_payboxhmac2_xml {
            get {
                return ResourceManager.GetString("cbmodeid_comment_pa_payboxhmac2_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Payment method ID PAYPAL.
        /// </summary>
        public static string cbmodeid_comment_pa_paypal_xml {
            get {
                return ResourceManager.GetString("cbmodeid_comment_pa_paypal_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Site rank.
        /// </summary>
        public static string cbmodeid_comment_pa_rang_xml {
            get {
                return ResourceManager.GetString("cbmodeid_comment_pa_rang_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Fill in this key if the client is undergoing a simplified migration with Atos: only include this key if the client’s contract does not start with 0210.
        /// </summary>
        public static string cbmodeid_comment_pa_s10reference_xml {
            get {
                return ResourceManager.GetString("cbmodeid_comment_pa_s10reference_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à The SMSDEFAULTREGION key corresponds to the default country code for SMS campaigns.
        /// </summary>
        public static string cbmodeid_comment_pa_smsdefaultregion_xml {
            get {
                return ResourceManager.GetString("cbmodeid_comment_pa_smsdefaultregion_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Payment method ID SOFORT.
        /// </summary>
        public static string cbmodeid_comment_pa_sofortberweisungde_xml {
            get {
                return ResourceManager.GetString("cbmodeid_comment_pa_sofortberweisungde_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Version number of the key used (usually = 1, see client’s back-office for the provider).
        /// </summary>
        public static string cbmodeid_comment_pa_versionkey_xml {
            get {
                return ResourceManager.GetString("cbmodeid_comment_pa_versionkey_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Payment method ID VISA.
        /// </summary>
        public static string cbmodeid_comment_pa_visa_xml {
            get {
                return ResourceManager.GetString("cbmodeid_comment_pa_visa_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Key provided by Sharegroop to generate the signature during the notification.
        /// </summary>
        public static string cbmodeid_comment_pa_webhooksalt_xml {
            get {
                return ResourceManager.GetString("cbmodeid_comment_pa_webhooksalt_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Payment method ID SOFORT.
        /// </summary>
        public static string cbmodeid_comment_pa15_sofortberweisungde_xml {
            get {
                return ResourceManager.GetString("cbmodeid_comment_pa15_sofortberweisungde_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Payment method ID PAYPAL.
        /// </summary>
        public static string cbmodeid_comment_paypal_xml {
            get {
                return ResourceManager.GetString("cbmodeid_comment_paypal_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Payment method ID SOFORT.
        /// </summary>
        public static string cbmodeid_comment_sofort_xml {
            get {
                return ResourceManager.GetString("cbmodeid_comment_sofort_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Payment method ID SOFORT.
        /// </summary>
        public static string cbmodeid_comment_sofortberweisungde_xml {
            get {
                return ResourceManager.GetString("cbmodeid_comment_sofortberweisungde_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à 9930002.
        /// </summary>
        public static string cbmodeid_comment_visa_xml {
            get {
                return ResourceManager.GetString("cbmodeid_comment_visa_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Payment method ID for installments.
        /// </summary>
        public static string cbmodeid_comment_xfois_xml {
            get {
                return ResourceManager.GetString("cbmodeid_comment_xfois_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Multiple payment index.
        /// </summary>
        public static string cbmodeidxfois_comment_idxfois_xml {
            get {
                return ResourceManager.GetString("cbmodeidxfois_comment_idxfois_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Changer de structure.
        /// </summary>
        public static string change_structure_button_btnDanger {
            get {
                return ResourceManager.GetString("change_structure_button_btnDanger", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à CHANGER DE STRUCTURE.
        /// </summary>
        public static string changeStructure_button_yes {
            get {
                return ResourceManager.GetString("changeStructure_button_yes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Error during verification: {0}.
        /// </summary>
        public static string check_cleanup_error_message {
            get {
                return ResourceManager.GetString("check_cleanup_error_message", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à No recent passage detected. Cleaning can be performed safely..
        /// </summary>
        public static string check_cleanup_no_recent_data_message {
            get {
                return ResourceManager.GetString("check_cleanup_no_recent_data_message", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Warning: recent data (less than 1 hour) detected. Cleanup is not allowed for safety reasons..
        /// </summary>
        public static string check_cleanup_recent_data_warning {
            get {
                return ResourceManager.GetString("check_cleanup_recent_data_warning", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Check the structure database connection.
        /// </summary>
        public static string check_database_connection {
            get {
                return ResourceManager.GetString("check_database_connection", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Chosen.
        /// </summary>
        public static string choisi_structure {
            get {
                return ResourceManager.GetString("choisi_structure", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Choose a partner.
        /// </summary>
        public static string choose_partner {
            get {
                return ResourceManager.GetString("choose_partner", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Choose a profile to add.
        /// </summary>
        public static string choose_profil_to_add {
            get {
                return ResourceManager.GetString("choose_profil_to_add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Choose structure.
        /// </summary>
        public static string choose_structure {
            get {
                return ResourceManager.GetString("choose_structure", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Error during cleanup: {0}.
        /// </summary>
        public static string clean_tables_error_message {
            get {
                return ResourceManager.GetString("clean_tables_error_message", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Cleanup could not be performed. Recent data is still present..
        /// </summary>
        public static string clean_tables_failed_message {
            get {
                return ResourceManager.GetString("clean_tables_failed_message", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Tables were successfully cleaned. All queuing table data has been deleted..
        /// </summary>
        public static string clean_tables_success_message {
            get {
                return ResourceManager.GetString("clean_tables_success_message", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Click on the cards to navigate.
        /// </summary>
        public static string cliquez_sur_les_cartes_pour_naviguer {
            get {
                return ResourceManager.GetString("cliquez_sur_les_cartes_pour_naviguer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Close.
        /// </summary>
        public static string close_modal {
            get {
                return ResourceManager.GetString("close_modal", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Blind copy address.
        /// </summary>
        public static string comment_email_blindcopyadresse_xml {
            get {
                return ResourceManager.GetString("comment_email_blindcopyadresse_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Copy address for purchase confirmation email.
        /// </summary>
        public static string comment_email_copyadresse_xml {
            get {
                return ResourceManager.GetString("comment_email_copyadresse_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Reply-to address.
        /// </summary>
        public static string comment_email_replyadresse_xml {
            get {
                return ResourceManager.GetString("comment_email_replyadresse_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Sender address for purchase confirmation email.
        /// </summary>
        public static string comment_email_senderadresse_xml {
            get {
                return ResourceManager.GetString("comment_email_senderadresse_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à ID of prices that display a specific text.
        /// </summary>
        public static string comment_marker_controlprice_xml {
            get {
                return ResourceManager.GetString("comment_marker_controlprice_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Display a specific text if print@home is selected.
        /// </summary>
        public static string comment_marker_printathome_xml {
            get {
                return ResourceManager.GetString("comment_marker_printathome_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Obsolete key group.
        /// </summary>
        public static string comment_navigation_accesfchoixseance_xml_ {
            get {
                return ResourceManager.GetString("comment_navigation_accesfchoixseance_xml ", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Obsolete key group.
        /// </summary>
        public static string comment_navigation_panier_xml {
            get {
                return ResourceManager.GetString("comment_navigation_panier_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Show unavailable seats.
        /// </summary>
        public static string comment_priseplacessurplan_showunavailableseats_xml {
            get {
                return ResourceManager.GetString("comment_priseplacessurplan_showunavailableseats_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Minimum chair X dimension.
        /// </summary>
        public static string comment_priseplacessurplan_startimagesfauteuilsx_xml {
            get {
                return ResourceManager.GetString("comment_priseplacessurplan_startimagesfauteuilsx_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Minimum chair Y dimension.
        /// </summary>
        public static string comment_priseplacessurplan_startimagesfauteuilsy_xml {
            get {
                return ResourceManager.GetString("comment_priseplacessurplan_startimagesfauteuilsy_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Mail server IP.
        /// </summary>
        public static string comment_smtpclient_ip_xml {
            get {
                return ResourceManager.GetString("comment_smtpclient_ip_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Error.
        /// </summary>
        public static string common_error {
            get {
                return ResourceManager.GetString("common_error", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Information.
        /// </summary>
        public static string common_info {
            get {
                return ResourceManager.GetString("common_info", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Hover over the tools to see the available modules.
        /// </summary>
        public static string common_survolez_outils_pour_voir_modules {
            get {
                return ResourceManager.GetString("common_survolez_outils_pour_voir_modules", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à View modules.
        /// </summary>
        public static string common_voir_modules_label {
            get {
                return ResourceManager.GetString("common_voir_modules_label", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Warning.
        /// </summary>
        public static string common_warning {
            get {
                return ResourceManager.GetString("common_warning", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Manage config.ini file.
        /// </summary>
        public static string config_ini {
            get {
                return ResourceManager.GetString("config-ini", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à The configuration key &apos;keyUniqueCodePromo&apos; is missing or empty.
        /// </summary>
        public static string config_keyuniquecodepromo_missing {
            get {
                return ResourceManager.GetString("config_keyuniquecodepromo_missing", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à The value(ConfigIni) must not be greater than the DB value..
        /// </summary>
        public static string config_value_exceedsDB_error {
            get {
                return ResourceManager.GetString("config_value_exceedsDB_error", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à  Waiting List Configuration.
        /// </summary>
        public static string configuration_listes_attente {
            get {
                return ResourceManager.GetString("configuration_listes_attente", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Confirm.
        /// </summary>
        public static string confirm_button {
            get {
                return ResourceManager.GetString("confirm_button", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Are you sure you want to delete the variable  &lt;strong&gt;&apos;{0}&apos;&lt;/strong&gt;?.
        /// </summary>
        public static string confirm_delete {
            get {
                return ResourceManager.GetString("confirm_delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Are you sure you want to delete this partner &lt;strong&gt;{0}&lt;/strong&gt;?.
        /// </summary>
        public static string confirm_delete_partner {
            get {
                return ResourceManager.GetString("confirm_delete_partner", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Are you sure you want to delete the variable &apos;{0}&apos;?.
        /// </summary>
        public static string confirm_delete_translation_area {
            get {
                return ResourceManager.GetString("confirm_delete_translation_area", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Are you sure you want to delete this variable?.
        /// </summary>
        public static string confirm_delete_variable {
            get {
                return ResourceManager.GetString("confirm_delete_variable", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à The time has expired. We will redirect you to the homepage.
        /// </summary>
        public static string confirm_dialog_message_time_expired {
            get {
                return ResourceManager.GetString("confirm_dialog_message_time_expired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à No.
        /// </summary>
        public static string confirm_dialog_no_button {
            get {
                return ResourceManager.GetString("confirm_dialog_no_button", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Yes .
        /// </summary>
        public static string confirm_dialog_yes_button {
            get {
                return ResourceManager.GetString("confirm_dialog_yes_button", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à I understand.
        /// </summary>
        public static string confirm_dialog_yes_button_text {
            get {
                return ResourceManager.GetString("confirm_dialog_yes_button_text", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Are you sure you want to restore the last saved file?.
        /// </summary>
        public static string confirm_restore_last_saved_file {
            get {
                return ResourceManager.GetString("confirm_restore_last_saved_file", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Are you sure you want to save the changes?.
        /// </summary>
        public static string confirm_save_changes_message {
            get {
                return ResourceManager.GetString("confirm_save_changes_message", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Are you sure you want to change the structure?.
        /// </summary>
        public static string confirm_switch_to_new_structure {
            get {
                return ResourceManager.GetString("confirm_switch_to_new_structure", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Confirmation.
        /// </summary>
        public static string confirmation {
            get {
                return ResourceManager.GetString("confirmation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Are you sure you want to delete the section.
        /// </summary>
        public static string confirmation_delete_section {
            get {
                return ResourceManager.GetString("confirmation_delete_section", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Pricing restriction per file.
        /// </summary>
        public static string contraintesventes_comment_restrictiontarifparfichier_xml {
            get {
                return ResourceManager.GetString("contraintesventes_comment_restrictiontarifparfichier_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Copy to PROD.
        /// </summary>
        public static string copy_to_prod {
            get {
                return ResourceManager.GetString("copy_to_prod", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Are you sure you want to copy the data to PROD?.
        /// </summary>
        public static string copy_to_prod_confirmation {
            get {
                return ResourceManager.GetString("copy_to_prod_confirmation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Copy to PROD.
        /// </summary>
        public static string copy_to_prod_title {
            get {
                return ResourceManager.GetString("copy_to_prod_title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Copy to TEST.
        /// </summary>
        public static string copy_to_test {
            get {
                return ResourceManager.GetString("copy_to_test", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Are you sure you want to copy the data to TEST?.
        /// </summary>
        public static string copy_to_test_confirmation {
            get {
                return ResourceManager.GetString("copy_to_test_confirmation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Copy to TEST.
        /// </summary>
        public static string copy_to_test_title {
            get {
                return ResourceManager.GetString("copy_to_test_title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Duration of future events sessions.
        /// </summary>
        public static string coupefile_comment_futures_events_sessions_duration_xml {
            get {
                return ResourceManager.GetString("coupefile_comment_futures_events_sessions_duration_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Comment message.
        /// </summary>
        public static string coupefile_comment_message_commentaire_xml {
            get {
                return ResourceManager.GetString("coupefile_comment_message_commentaire_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à No coupon could be created. Please try again..
        /// </summary>
        public static string coupon_aucun_oupon_cree {
            get {
                return ResourceManager.GetString("coupon_aucun_oupon_cree", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à {0} coupon(s) successfully created for {1}..
        /// </summary>
        public static string coupon_creation_succes {
            get {
                return ResourceManager.GetString("coupon_creation_succes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Error while creating coupons: {0}.
        /// </summary>
        public static string coupon_erreur_creation {
            get {
                return ResourceManager.GetString("coupon_erreur_creation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Coupons for {0} have been successfully exported..
        /// </summary>
        public static string coupon_export_success {
            get {
                return ResourceManager.GetString("coupon_export_success", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à {0} coupon(s) successfully exported for {1}.
        /// </summary>
        public static string coupon_export_success_avec_filtre {
            get {
                return ResourceManager.GetString("coupon_export_success_avec_filtre", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Active.
        /// </summary>
        public static string coupon_list_active {
            get {
                return ResourceManager.GetString("coupon_list_active", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Code.
        /// </summary>
        public static string coupon_list_code {
            get {
                return ResourceManager.GetString("coupon_list_code", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Order ID.
        /// </summary>
        public static string coupon_list_commande_id {
            get {
                return ResourceManager.GetString("coupon_list_commande_id", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Creation date.
        /// </summary>
        public static string coupon_list_creation_date {
            get {
                return ResourceManager.GetString("coupon_list_creation_date", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Day (s).
        /// </summary>
        public static string coupon_list_days {
            get {
                return ResourceManager.GetString("coupon_list_days", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Expiration date.
        /// </summary>
        public static string coupon_list_expiration_date {
            get {
                return ResourceManager.GetString("coupon_list_expiration_date", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à day(s).
        /// </summary>
        public static string coupon_list_expired {
            get {
                return ResourceManager.GetString("coupon_list_expired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Expired since.
        /// </summary>
        public static string coupon_list_expired_since {
            get {
                return ResourceManager.GetString("coupon_list_expired_since", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Expires in.
        /// </summary>
        public static string coupon_list_expires_in {
            get {
                return ResourceManager.GetString("coupon_list_expires_in", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à No active coupons.
        /// </summary>
        public static string coupon_list_no_active_coupons {
            get {
                return ResourceManager.GetString("coupon_list_no_active_coupons", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à No expired coupons.
        /// </summary>
        public static string coupon_list_no_expired_coupons {
            get {
                return ResourceManager.GetString("coupon_list_no_expired_coupons", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Coupon list – No used coupons.
        /// </summary>
        public static string coupon_list_no_used_coupons {
            get {
                return ResourceManager.GetString("coupon_list_no_used_coupons", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Start date.
        /// </summary>
        public static string coupon_list_start_date {
            get {
                return ResourceManager.GetString("coupon_list_start_date", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Status.
        /// </summary>
        public static string coupon_list_status {
            get {
                return ResourceManager.GetString("coupon_list_status", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Coupon used.
        /// </summary>
        public static string coupon_list_used {
            get {
                return ResourceManager.GetString("coupon_list_used", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Used date.
        /// </summary>
        public static string coupon_list_used_date {
            get {
                return ResourceManager.GetString("coupon_list_used_date", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à active coupon(s).
        /// </summary>
        public static string coupon_status_active {
            get {
                return ResourceManager.GetString("coupon_status_active", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Coupon status expired.
        /// </summary>
        public static string coupon_status_expired {
            get {
                return ResourceManager.GetString("coupon_status_expired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à No coupon.
        /// </summary>
        public static string coupon_status_none {
            get {
                return ResourceManager.GetString("coupon_status_none", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Coupon status used.
        /// </summary>
        public static string coupon_status_used {
            get {
                return ResourceManager.GetString("coupon_status_used", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Active.
        /// </summary>
        public static string couponstatus_active {
            get {
                return ResourceManager.GetString("couponstatus_active", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Expired.
        /// </summary>
        public static string couponstatus_expired {
            get {
                return ResourceManager.GetString("couponstatus_expired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à  Undefined.
        /// </summary>
        public static string couponstatus_undefined {
            get {
                return ResourceManager.GetString("couponstatus_undefined", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Used.
        /// </summary>
        public static string couponstatus_used {
            get {
                return ResourceManager.GetString("couponstatus_used", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Attention.
        /// </summary>
        public static string create_coupon_attention {
            get {
                return ResourceManager.GetString("create_coupon_attention", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Please create a liaison first..
        /// </summary>
        public static string create_liaison_first_message {
            get {
                return ResourceManager.GetString("create_liaison_first_message", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Create New Link.
        /// </summary>
        public static string create_new_liaison {
            get {
                return ResourceManager.GetString("create_new_liaison", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Create a new structure.
        /// </summary>
        public static string create_new_structure {
            get {
                return ResourceManager.GetString("create-new-structure", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Subscription - Create Profile - Sector.
        /// </summary>
        public static string createdossierproduits_comment_filiereid_xml {
            get {
                return ResourceManager.GetString("createdossierproduits_comment_filiereid_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Unique email (0: No, 1: Yes, -1: Disable) for client profile.
        /// </summary>
        public static string createprofil_comment_mailunicity_xml {
            get {
                return ResourceManager.GetString("createprofil_comment_mailunicity_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à  Online Sales - Create Profile - Sector.
        /// </summary>
        public static string createprofil_comment_webfiliereid_xml {
            get {
                return ResourceManager.GetString("createprofil_comment_webfiliereid_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Subscription - Create Profile - Sector.
        /// </summary>
        public static string createprofilabo_comment_webfiliereid_xml {
            get {
                return ResourceManager.GetString("createprofilabo_comment_webfiliereid_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Email uniqueness for profile.
        /// </summary>
        public static string createprofillink_comment_mailunicity_xml_ {
            get {
                return ResourceManager.GetString("createprofillink_comment_mailunicity_xml ", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Order Creation.
        /// </summary>
        public static string creation_commande {
            get {
                return ResourceManager.GetString("creation_commande", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Salt.
        /// </summary>
        public static string crmexterne_comment_salt_xml {
            get {
                return ResourceManager.GetString("crmexterne_comment_salt_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Type.
        /// </summary>
        public static string crmexterne_comment_type_xml {
            get {
                return ResourceManager.GetString("crmexterne_comment_type_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à  URL.
        /// </summary>
        public static string crmexterne_comment_url_xml {
            get {
                return ResourceManager.GetString("crmexterne_comment_url_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à InfoComp Login.
        /// </summary>
        public static string customerarea_comment_checkinfocomplogin_xml {
            get {
                return ResourceManager.GetString("customerarea_comment_checkinfocomplogin_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à List of available infocomp.
        /// </summary>
        public static string customerarea_comment_listinfocompdisponible_xml {
            get {
                return ResourceManager.GetString("customerarea_comment_listinfocompdisponible_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Customer area version.
        /// </summary>
        public static string customerarea_comment_version_xml {
            get {
                return ResourceManager.GetString("customerarea_comment_version_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Database connection issue.
        /// </summary>
        public static string database_connection_issue {
            get {
                return ResourceManager.GetString("database_connection_issue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à The start date is required..
        /// </summary>
        public static string date_debut_required {
            get {
                return ResourceManager.GetString("date_debut_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à The expiration date is required..
        /// </summary>
        public static string date_expiration_required {
            get {
                return ResourceManager.GetString("date_expiration_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Deflag delay (automatic).
        /// </summary>
        public static string Delai_Deflag {
            get {
                return ResourceManager.GetString("Delai_Deflag", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Deflag delay = Expiration time + 10 minutes (automatically calculated)..
        /// </summary>
        public static string Delai_Deflag_Description {
            get {
                return ResourceManager.GetString("Delai_Deflag_Description", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Deflagration delay is null.
        /// </summary>
        public static string delai_deflag_null_error {
            get {
                return ResourceManager.GetString("delai_deflag_null_error", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Error deleting variable &lt;strong&gt;&apos;{0}&apos;: {1}&lt;/strong&gt;.
        /// </summary>
        public static string delete_error {
            get {
                return ResourceManager.GetString("delete_error", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Deletion Error.
        /// </summary>
        public static string delete_error_title {
            get {
                return ResourceManager.GetString("delete_error_title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à An error occurred while deleting the translation key {0}..
        /// </summary>
        public static string delete_errorm_message {
            get {
                return ResourceManager.GetString("delete_errorm_message", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Variable &lt;strong&gt;&apos;{0}&apos;&lt;/strong&gt; deleted successfully..
        /// </summary>
        public static string delete_success {
            get {
                return ResourceManager.GetString("delete_success", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à The translation key {0} has been successfully deleted..
        /// </summary>
        public static string delete_success_message {
            get {
                return ResourceManager.GetString("delete_success_message", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Deletion Successful.
        /// </summary>
        public static string delete_success_title {
            get {
                return ResourceManager.GetString("delete_success_title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à  Manages widget display and order..
        /// </summary>
        public static string description_gestion_widgets {
            get {
                return ResourceManager.GetString("description_gestion_widgets", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Are you sure you want to delete the translation {0}?.
        /// </summary>
        public static string dialog_delete_message {
            get {
                return ResourceManager.GetString("dialog_delete_message", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Choose a Section....
        /// </summary>
        public static string dropdown_choose_section {
            get {
                return ResourceManager.GetString("dropdown_choose_section", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Key Group Filter.
        /// </summary>
        public static string dropdown_filter_groupKey_fieldName {
            get {
                return ResourceManager.GetString("dropdown_filter_groupKey_fieldName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à ECCM key for integration.
        /// </summary>
        public static string edcampaign_comment_eccmkey_xml {
            get {
                return ResourceManager.GetString("edcampaign_comment_eccmkey_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à List of email providers.
        /// </summary>
        public static string edcampaign_comment_presta_xml {
            get {
                return ResourceManager.GetString("edcampaign_comment_presta_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Edit a partner.
        /// </summary>
        public static string edit_partenaire {
            get {
                return ResourceManager.GetString("edit_partenaire", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Edit a translation.
        /// </summary>
        public static string edit_translation {
            get {
                return ResourceManager.GetString("edit_translation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Edit a variable.
        /// </summary>
        public static string edit_variable {
            get {
                return ResourceManager.GetString("edit_variable", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Order Edition.
        /// </summary>
        public static string edition_commande {
            get {
                return ResourceManager.GetString("edition_commande", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Blind copy address.
        /// </summary>
        public static string emailerror_comment_blindcopyadresse_xml {
            get {
                return ResourceManager.GetString("emailerror_comment_blindcopyadresse_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Copy address.
        /// </summary>
        public static string emailerror_comment_copyadresse_xml {
            get {
                return ResourceManager.GetString("emailerror_comment_copyadresse_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Sender for registration email.
        /// </summary>
        public static string emailerror_comment_inscriptionsenderadresse_xml {
            get {
                return ResourceManager.GetString("emailerror_comment_inscriptionsenderadresse_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Sender for forgotten password email.
        /// </summary>
        public static string emailerror_comment_passwordcopyadresse_xml {
            get {
                return ResourceManager.GetString("emailerror_comment_passwordcopyadresse_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Reply-to address.
        /// </summary>
        public static string emailerror_comment_replyadresse_xml {
            get {
                return ResourceManager.GetString("emailerror_comment_replyadresse_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Sender address.
        /// </summary>
        public static string emailerror_comment_senderadresse_xml {
            get {
                return ResourceManager.GetString("emailerror_comment_senderadresse_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Copy address for registration email.
        /// </summary>
        public static string emailinscription_comment_copyadresse_xml {
            get {
                return ResourceManager.GetString("emailinscription_comment_copyadresse_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Reply address for registration email.
        /// </summary>
        public static string emailinscription_comment_replyadresse_xml {
            get {
                return ResourceManager.GetString("emailinscription_comment_replyadresse_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Sender of the registration email.
        /// </summary>
        public static string emailinscription_comment_senderadresse_xml {
            get {
                return ResourceManager.GetString("emailinscription_comment_senderadresse_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Copy address.
        /// </summary>
        public static string emailkiosk_comment_copyadresse_xml {
            get {
                return ResourceManager.GetString("emailkiosk_comment_copyadresse_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Overwrite user address.
        /// </summary>
        public static string emailkiosk_comment_overwriteuseradresse_xml {
            get {
                return ResourceManager.GetString("emailkiosk_comment_overwriteuseradresse_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Reply address.
        /// </summary>
        public static string emailkiosk_comment_replyadresse_xml {
            get {
                return ResourceManager.GetString("emailkiosk_comment_replyadresse_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Sender address.
        /// </summary>
        public static string emailkiosk_comment_senderadresse_xml {
            get {
                return ResourceManager.GetString("emailkiosk_comment_senderadresse_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Tracking address.
        /// </summary>
        public static string emailkiosk_comment_trakingreceiver_xml {
            get {
                return ResourceManager.GetString("emailkiosk_comment_trakingreceiver_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Copy address .
        /// </summary>
        public static string emailpassword_comment_copyadresse_xml {
            get {
                return ResourceManager.GetString("emailpassword_comment_copyadresse_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Reply address.
        /// </summary>
        public static string emailpassword_comment_replyadresse_xml {
            get {
                return ResourceManager.GetString("emailpassword_comment_replyadresse_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Sender of the email.
        /// </summary>
        public static string emailpassword_comment_senderadresse_xml {
            get {
                return ResourceManager.GetString("emailpassword_comment_senderadresse_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Copy address.
        /// </summary>
        public static string emailseuilmini_comment_copyadresse_xml {
            get {
                return ResourceManager.GetString("emailseuilmini_comment_copyadresse_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Reply address.
        /// </summary>
        public static string emailseuilmini_comment_replyadresse_xml {
            get {
                return ResourceManager.GetString("emailseuilmini_comment_replyadresse_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Sender of the email.
        /// </summary>
        public static string emailseuilmini_comment_sendadresse_xml {
            get {
                return ResourceManager.GetString("emailseuilmini_comment_sendadresse_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Email sender.
        /// </summary>
        public static string emailseuilmini_comment_senderadresse_xml {
            get {
                return ResourceManager.GetString("emailseuilmini_comment_senderadresse_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Copy address.
        /// </summary>
        public static string emailsupportingdocuments_comment_copyadresse_xml {
            get {
                return ResourceManager.GetString("emailsupportingdocuments_comment_copyadresse_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Receiver address.
        /// </summary>
        public static string emailsupportingdocuments_comment_receiveradresse_xml {
            get {
                return ResourceManager.GetString("emailsupportingdocuments_comment_receiveradresse_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Reply address.
        /// </summary>
        public static string emailsupportingdocuments_comment_replyadresse_xml {
            get {
                return ResourceManager.GetString("emailsupportingdocuments_comment_replyadresse_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Sender address.
        /// </summary>
        public static string emailsupportingdocuments_comment_senderadresse_xml {
            get {
                return ResourceManager.GetString("emailsupportingdocuments_comment_senderadresse_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à SMTP client server IP.
        /// </summary>
        public static string emailsupportingdocuments_comment_smtpclientip_xml {
            get {
                return ResourceManager.GetString("emailsupportingdocuments_comment_smtpclientip_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Enter structure ID.
        /// </summary>
        public static string enter_structure_id {
            get {
                return ResourceManager.GetString("enter_structure_id", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Email Sending.
        /// </summary>
        public static string envoi_mail {
            get {
                return ResourceManager.GetString("envoi_mail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Error loading coupons: {0}.
        /// </summary>
        public static string erreur_chargement_coupons {
            get {
                return ResourceManager.GetString("erreur_chargement_coupons", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à &quot;Error loading data: {0}.
        /// </summary>
        public static string erreur_chargement_donnees {
            get {
                return ResourceManager.GetString("erreur_chargement_donnees", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Error exporting coupons: {0}.
        /// </summary>
        public static string erreur_exportation_coupons {
            get {
                return ResourceManager.GetString("erreur_exportation_coupons", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Error downloading file: {0}.
        /// </summary>
        public static string erreur_telechargement_fichier {
            get {
                return ResourceManager.GetString("erreur_telechargement_fichier", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Failed to assign roles: {0}.
        /// </summary>
        public static string error_assign_roles_message {
            get {
                return ResourceManager.GetString("error_assign_roles_message", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Error assigning roles.
        /// </summary>
        public static string error_assign_roles_title {
            get {
                return ResourceManager.GetString("error_assign_roles_title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Unable to create group {0}. Please try again..
        /// </summary>
        public static string error_create_group_message {
            get {
                return ResourceManager.GetString("error_create_group_message", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Error creating group.
        /// </summary>
        public static string error_create_group_title {
            get {
                return ResourceManager.GetString("error_create_group_title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Unable to create module {0}. Please try again..
        /// </summary>
        public static string error_create_module_message {
            get {
                return ResourceManager.GetString("error_create_module_message", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Unable to create role {0}. Please try again..
        /// </summary>
        public static string error_create_role_message {
            get {
                return ResourceManager.GetString("error_create_role_message", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Error creating role.
        /// </summary>
        public static string error_create_role_title {
            get {
                return ResourceManager.GetString("error_create_role_title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Error.
        /// </summary>
        public static string error_exist_partener_title {
            get {
                return ResourceManager.GetString("error_exist_partener_title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Error: {0}Error: {0}.
        /// </summary>
        public static string error_generic {
            get {
                return ResourceManager.GetString("error_generic", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à The group {0} already exists..
        /// </summary>
        public static string error_group_exists {
            get {
                return ResourceManager.GetString("error_group_exists", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Invalid structure ID: {0}.
        /// </summary>
        public static string error_invalid_structure_id {
            get {
                return ResourceManager.GetString("error_invalid_structure_id", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à An error occurred while loading data: {0}.
        /// </summary>
        public static string error_loading_data_message {
            get {
                return ResourceManager.GetString("error_loading_data_message", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Error loading data.
        /// </summary>
        public static string error_loading_data_title {
            get {
                return ResourceManager.GetString("error_loading_data_title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Error during initial loading: {0}.
        /// </summary>
        public static string error_loading_initial {
            get {
                return ResourceManager.GetString("error_loading_initial", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Error loading structure profiles: {0}.
        /// </summary>
        public static string error_loading_structure_profiles {
            get {
                return ResourceManager.GetString("error_loading_structure_profiles", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Error loading structures: {0}.
        /// </summary>
        public static string error_loading_structures {
            get {
                return ResourceManager.GetString("error_loading_structures", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à The module {0} already exists..
        /// </summary>
        public static string error_module_exists {
            get {
                return ResourceManager.GetString("error_module_exists", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à The partner &apos;{0}&apos; already exists..
        /// </summary>
        public static string error_partner_exists {
            get {
                return ResourceManager.GetString("error_partner_exists", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Please select at least one buyer profile to link..
        /// </summary>
        public static string error_please_select_profile {
            get {
                return ResourceManager.GetString("error_please_select_profile", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Please select a structure..
        /// </summary>
        public static string error_please_select_structure {
            get {
                return ResourceManager.GetString("error_please_select_structure", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à The role {0} already exists..
        /// </summary>
        public static string error_role_exists {
            get {
                return ResourceManager.GetString("error_role_exists", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Error saving: {0}.
        /// </summary>
        public static string error_saving_liaison {
            get {
                return ResourceManager.GetString("error_saving_liaison", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Error searching for partners.
        /// </summary>
        public static string error_search_partners_title {
            get {
                return ResourceManager.GetString("error_search_partners_title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Error selecting reseller: {0}.
        /// </summary>
        public static string error_selecting_revendeur {
            get {
                return ResourceManager.GetString("error_selecting_revendeur", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Exclusion.
        /// </summary>
        public static string exclusion {
            get {
                return ResourceManager.GetString("exclusion", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Exclusions.
        /// </summary>
        public static string exclusions {
            get {
                return ResourceManager.GetString("exclusions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Expired coupons only.
        /// </summary>
        public static string expired_coupons_only {
            get {
                return ResourceManager.GetString("expired_coupons_only", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Export active coupons by period.
        /// </summary>
        public static string export_coupons_actifs_periode {
            get {
                return ResourceManager.GetString("export_coupons_actifs_periode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Export for profile:.
        /// </summary>
        public static string export_pour_profil {
            get {
                return ResourceManager.GetString("export_pour_profil", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Export only active coupons (not expired and not used).
        /// </summary>
        public static string exporter_seulement_coupons_actifs {
            get {
                return ResourceManager.GetString("exporter_seulement_coupons_actifs", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à facebook_comment_applicationsecret_xml.
        /// </summary>
        public static string facebook_comment_applicationsecret_xml {
            get {
                return ResourceManager.GetString("facebook_comment_applicationsecret_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Facebook dev application secret.
        /// </summary>
        public static string facebookdev_comment_applicationsecret_xml {
            get {
                return ResourceManager.GetString("facebookdev_comment_applicationsecret_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Invoice payment method.
        /// </summary>
        public static string facture_mode_comment_facturemode_id_xml {
            get {
                return ResourceManager.GetString("facture_mode_comment_facturemode_id_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Duplicate email address detected..
        /// </summary>
        public static string Field_Validation_DuplicateEmail {
            get {
                return ResourceManager.GetString("Field_Validation_DuplicateEmail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Duplicate email addresses detected: {0}.
        /// </summary>
        public static string Field_Validation_DuplicateEmail_WithDetails {
            get {
                return ResourceManager.GetString("Field_Validation_DuplicateEmail_WithDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Invalid email address.
        /// </summary>
        public static string Field_Validation_InvalidEmail {
            get {
                return ResourceManager.GetString("Field_Validation_InvalidEmail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Invalid email address: {0}.
        /// </summary>
        public static string Field_Validation_InvalidEmail_WithDetails {
            get {
                return ResourceManager.GetString("Field_Validation_InvalidEmail_WithDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à The REPLYADRESSE field can contain only one email address.
        /// </summary>
        public static string Field_Validation_ReplyAddress_SingleEmail {
            get {
                return ResourceManager.GetString("Field_Validation_ReplyAddress_SingleEmail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à The field can contain only one email address.
        /// </summary>
        public static string Field_Validation_SingleEmail {
            get {
                return ResourceManager.GetString("Field_Validation_SingleEmail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à File does not exist.
        /// </summary>
        public static string file_does_not_exist {
            get {
                return ResourceManager.GetString("file_does_not_exist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à The file is currently in use by {0} on Structure No. {1}, please change the structure !.
        /// </summary>
        public static string file_in_use_message {
            get {
                return ResourceManager.GetString("file_in_use_message", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Filter by status.
        /// </summary>
        public static string filter_by_status {
            get {
                return ResourceManager.GetString("filter_by_status", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à For all languages.
        /// </summary>
        public static string for_all_languages {
            get {
                return ResourceManager.GetString("for_all_languages", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à For this language.
        /// </summary>
        public static string for_this_language {
            get {
                return ResourceManager.GetString("for_this_language", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à ID of the list of available formulas.
        /// </summary>
        public static string formule_comment_listeformuleid_xml {
            get {
                return ResourceManager.GetString("formule_comment_listeformuleid_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à From partners page.
        /// </summary>
        public static string from_partners_page {
            get {
                return ResourceManager.GetString("from_partners_page", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Full list.
        /// </summary>
        public static string full_list {
            get {
                return ResourceManager.GetString("full_list", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Promotional Coupon Management - Structure.
        /// </summary>
        public static string general_title {
            get {
                return ResourceManager.GetString("general_title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Coupon manager.
        /// </summary>
        public static string gestion_coupons_promo {
            get {
                return ResourceManager.GetString("gestion-coupons-promo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Template Management (Abo).
        /// </summary>
        public static string gestion_maquette_abo_fermer {
            get {
                return ResourceManager.GetString("gestion-maquette-abo-fermer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à WebTracing Management.
        /// </summary>
        public static string gestion_webtracing {
            get {
                return ResourceManager.GetString("gestion-webtracing", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Widget Management.
        /// </summary>
        public static string gestion_widgets {
            get {
                return ResourceManager.GetString("gestion_widgets", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Actions.
        /// </summary>
        public static string gestionmaquette_actions {
            get {
                return ResourceManager.GetString("gestionmaquette_actions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Existing associations.
        /// </summary>
        public static string gestionmaquette_existing_associations {
            get {
                return ResourceManager.GetString("gestionmaquette_existing_associations", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Formula.
        /// </summary>
        public static string gestionmaquette_formule {
            get {
                return ResourceManager.GetString("gestionmaquette_formule", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Loading....
        /// </summary>
        public static string gestionmaquette_loading {
            get {
                return ResourceManager.GetString("gestionmaquette_loading", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à New link between layout, session, and subscription.
        /// </summary>
        public static string gestionmaquette_new_association {
            get {
                return ResourceManager.GetString("gestionmaquette_new_association", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à No associations found.
        /// </summary>
        public static string gestionmaquette_no_associations_found {
            get {
                return ResourceManager.GetString("gestionmaquette_no_associations_found", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Operation date.
        /// </summary>
        public static string gestionmaquette_operation_date {
            get {
                return ResourceManager.GetString("gestionmaquette_operation_date", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Refresh.
        /// </summary>
        public static string gestionmaquette_refresh {
            get {
                return ResourceManager.GetString("gestionmaquette_refresh", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Save.
        /// </summary>
        public static string gestionmaquette_save {
            get {
                return ResourceManager.GetString("gestionmaquette_save", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Select a formula.
        /// </summary>
        public static string gestionmaquette_select_formula {
            get {
                return ResourceManager.GetString("gestionmaquette_select_formula", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Select a session.
        /// </summary>
        public static string gestionmaquette_select_session {
            get {
                return ResourceManager.GetString("gestionmaquette_select_session", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Select a template.
        /// </summary>
        public static string gestionmaquette_select_template {
            get {
                return ResourceManager.GetString("gestionmaquette_select_template", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Session.
        /// </summary>
        public static string gestionmaquette_session {
            get {
                return ResourceManager.GetString("gestionmaquette_session", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Subscription Formula.
        /// </summary>
        public static string gestionmaquette_subscription_formule {
            get {
                return ResourceManager.GetString("gestionmaquette_subscription_formule", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Template.
        /// </summary>
        public static string gestionmaquette_template {
            get {
                return ResourceManager.GetString("gestionmaquette_template", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Template management for closed subscription.
        /// </summary>
        public static string gestionmaquette_title {
            get {
                return ResourceManager.GetString("gestionmaquette_title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à This association already exists.
        /// </summary>
        public static string gestionmaquetteabofermer_message_association_already_exists {
            get {
                return ResourceManager.GetString("gestionmaquetteabofermer_message_association_already_exists", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à  Association deleted successfully.
        /// </summary>
        public static string gestionmaquetteabofermer_message_association_deleted_successfully {
            get {
                return ResourceManager.GetString("gestionmaquetteabofermer_message_association_deleted_successfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Association saved successfully.
        /// </summary>
        public static string gestionmaquetteabofermer_message_association_saved_successfully {
            get {
                return ResourceManager.GetString("gestionmaquetteabofermer_message_association_saved_successfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Error.
        /// </summary>
        public static string gestionmaquetteabofermer_message_error {
            get {
                return ResourceManager.GetString("gestionmaquetteabofermer_message_error", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Error deleting the association.
        /// </summary>
        public static string gestionmaquetteabofermer_message_error_deleting_association {
            get {
                return ResourceManager.GetString("gestionmaquetteabofermer_message_error_deleting_association", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Error loading associations.
        /// </summary>
        public static string gestionmaquetteabofermer_message_error_loading_associations {
            get {
                return ResourceManager.GetString("gestionmaquetteabofermer_message_error_loading_associations", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Error loading data.
        /// </summary>
        public static string gestionmaquetteabofermer_message_error_loading_data {
            get {
                return ResourceManager.GetString("gestionmaquetteabofermer_message_error_loading_data", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Error saving the association.
        /// </summary>
        public static string gestionmaquetteabofermer_message_error_saving_association {
            get {
                return ResourceManager.GetString("gestionmaquetteabofermer_message_error_saving_association", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Please select all items.
        /// </summary>
        public static string gestionmaquetteabofermer_message_please_select_all {
            get {
                return ResourceManager.GetString("gestionmaquetteabofermer_message_please_select_all", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Sections.
        /// </summary>
        public static string h1_sections {
            get {
                return ResourceManager.GetString("h1_sections", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Variables.
        /// </summary>
        public static string h1_variables {
            get {
                return ResourceManager.GetString("h1_variables", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Access.
        /// </summary>
        public static string home_btn_primary_acceder {
            get {
                return ResourceManager.GetString("home_btn_primary_acceder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Explore the available features above to start boosting your productivity right now..
        /// </summary>
        public static string home_explorer_fonctionnalite {
            get {
                return ResourceManager.GetString("home_explorer_fonctionnalite", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Quickly access your platform’s essential features.
        /// </summary>
        public static string home_outil_gesion_tst {
            get {
                return ResourceManager.GetString("home_outil_gesion_tst", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Ready to optimize your workflow?.
        /// </summary>
        public static string home_worflow_questions {
            get {
                return ResourceManager.GetString("home_worflow_questions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Search.
        /// </summary>
        public static string icon_search {
            get {
                return ResourceManager.GetString("icon_search", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Inclusion.
        /// </summary>
        public static string inclusion {
            get {
                return ResourceManager.GetString("inclusion", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Inclusions.
        /// </summary>
        public static string inclusions {
            get {
                return ResourceManager.GetString("inclusions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Force login for event list.
        /// </summary>
        public static string indiv_comment_forcerloginlistemanifs_xml {
            get {
                return ResourceManager.GetString("indiv_comment_forcerloginlistemanifs_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Force login for cart.
        /// </summary>
        public static string indiv_comment_forcerloginpanier_xml {
            get {
                return ResourceManager.GetString("indiv_comment_forcerloginpanier_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Display buyer&apos;s profile in popup.
        /// </summary>
        public static string indiv_comment_openprofilacheteurpopup_xml {
            get {
                return ResourceManager.GetString("indiv_comment_openprofilacheteurpopup_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Credit card mode ID.
        /// </summary>
        public static string kiosq_comment_cbmodeidid_xml {
            get {
                return ResourceManager.GetString("kiosq_comment_cbmodeidid_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Sector ID.
        /// </summary>
        public static string kiosq_comment_filiereid_xml {
            get {
                return ResourceManager.GetString("kiosq_comment_filiereid_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Point of sale ID.
        /// </summary>
        public static string kiosq_comment_postid_xml {
            get {
                return ResourceManager.GetString("kiosq_comment_postid_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Rate reference ID.
        /// </summary>
        public static string kiosq_comment_tarifidref_xml {
            get {
                return ResourceManager.GetString("kiosq_comment_tarifidref_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Operator used by the KIOSQ.
        /// </summary>
        public static string kiosq_comment_weboperatorid_xml {
            get {
                return ResourceManager.GetString("kiosq_comment_weboperatorid_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Create a section.
        /// </summary>
        public static string label_create_section {
            get {
                return ResourceManager.GetString("label_create_section", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Description.
        /// </summary>
        public static string label_description_section {
            get {
                return ResourceManager.GetString("label_description_section", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Description.
        /// </summary>
        public static string label_description_variable {
            get {
                return ResourceManager.GetString("label_description_variable", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Section key.
        /// </summary>
        public static string label_key_section {
            get {
                return ResourceManager.GetString("label_key_section", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Name.
        /// </summary>
        public static string label_name_partener {
            get {
                return ResourceManager.GetString("label_name_partener", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Roles.
        /// </summary>
        public static string label_partener_roles {
            get {
                return ResourceManager.GetString("label_partener_roles", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Structures.
        /// </summary>
        public static string label_partener_structure {
            get {
                return ResourceManager.GetString("label_partener_structure", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Roles.
        /// </summary>
        public static string label_roles {
            get {
                return ResourceManager.GetString("label_roles", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Structures.
        /// </summary>
        public static string label_structures {
            get {
                return ResourceManager.GetString("label_structures", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Manage translations from the ABO platform.
        /// </summary>
        public static string label_translation_abo_platform {
            get {
                return ResourceManager.GetString("label_translation_abo_platform", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à {0} translation(s).
        /// </summary>
        public static string label_translation_count {
            get {
                return ResourceManager.GetString("label_translation_count", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Manage translations from the CUSTOMER platform.
        /// </summary>
        public static string label_translation_customer_platform {
            get {
                return ResourceManager.GetString("label_translation_customer_platform", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Manage translation INDIV platform.
        /// </summary>
        public static string label_translation_indiv_platform {
            get {
                return ResourceManager.GetString("label_translation_indiv_platform", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Variable name.
        /// </summary>
        public static string label_variable_name {
            get {
                return ResourceManager.GetString("label_variable_name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à link reseller-pro buyer.
        /// </summary>
        public static string liaison_acheteurr_revendeur {
            get {
                return ResourceManager.GetString("liaison-acheteurr-revendeur", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Link details.
        /// </summary>
        public static string liaison_details {
            get {
                return ResourceManager.GetString("liaison_details", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Create links between resellers and buyer profiles on structures.
        /// </summary>
        public static string liaison_revendeur_description {
            get {
                return ResourceManager.GetString("liaison_revendeur_description", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Description of the new reseller linkage workflow.
        /// </summary>
        public static string liaison_revendeur_description_new_workflow {
            get {
                return ResourceManager.GetString("liaison_revendeur_description_new_workflow", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Reseller-Buyer Profile Link.
        /// </summary>
        public static string liaison_revendeur_title {
            get {
                return ResourceManager.GetString("liaison_revendeur_title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Link saved successfully!.
        /// </summary>
        public static string liaison_saved_successfully {
            get {
                return ResourceManager.GetString("liaison_saved_successfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Link all profiles.
        /// </summary>
        public static string link_all_button {
            get {
                return ResourceManager.GetString("link_all_button", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Link the reseller to the buyer profile.
        /// </summary>
        public static string link_revendeur_to_profil_acheteur {
            get {
                return ResourceManager.GetString("link_revendeur_to_profil_acheteur", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à linked structure(s).
        /// </summary>
        public static string linked_structures {
            get {
                return ResourceManager.GetString("linked_structures", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Linked to an existing key.
        /// </summary>
        public static string linked_to_existing_key {
            get {
                return ResourceManager.GetString("linked_to_existing_key", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à  Offer Catalogs.
        /// </summary>
        public static string liste_catalogues_offres {
            get {
                return ResourceManager.GetString("liste_catalogues_offres", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Loading.
        /// </summary>
        public static string loading {
            get {
                return ResourceManager.GetString("loading", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Loading data....
        /// </summary>
        public static string loading_data {
            get {
                return ResourceManager.GetString("loading_data", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Error while loading:.
        /// </summary>
        public static string loading_error {
            get {
                return ResourceManager.GetString("loading_error", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Loading profiles.
        /// </summary>
        public static string loading_profiles {
            get {
                return ResourceManager.GetString("loading_profiles", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Loading data....
        /// </summary>
        public static string loading_spinner_message_text {
            get {
                return ResourceManager.GetString("loading_spinner_message_text", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Loading....
        /// </summary>
        public static string loading_spinner_span {
            get {
                return ResourceManager.GetString("loading_spinner_span", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Loading....
        /// </summary>
        public static string loading_spinner_visually_hidden_text {
            get {
                return ResourceManager.GetString("loading_spinner_visually_hidden_text", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Mandatory Configuration.
        /// </summary>
        public static string mandatory_sections {
            get {
                return ResourceManager.GetString("mandatory_sections", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à This field is required.
        /// </summary>
        public static string message_champ_obligatoire_xml {
            get {
                return ResourceManager.GetString("message_champ_obligatoire_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à  You requested to generate more than 2000 coupons. Generation may take longer. Do you want to continue?.
        /// </summary>
        public static string modal_confirm_generation_more_than_2000 {
            get {
                return ResourceManager.GetString("modal.confirm_generation_more_than_2000", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Counpons For.
        /// </summary>
        public static string modal_coupons_title {
            get {
                return ResourceManager.GetString("modal_coupons_title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Create promotional coupons for.
        /// </summary>
        public static string modal_create_title {
            get {
                return ResourceManager.GetString("modal_create_title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Common expiration date.
        /// </summary>
        public static string modal_end_date {
            get {
                return ResourceManager.GetString("modal_end_date", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à The expiration date must be after the start date.
        /// </summary>
        public static string modal_end_date_error {
            get {
                return ResourceManager.GetString("modal_end_date_error", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Example code.
        /// </summary>
        public static string modal_example_code {
            get {
                return ResourceManager.GetString("modal_example_code", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Example code help.
        /// </summary>
        public static string modal_example_code_help {
            get {
                return ResourceManager.GetString("modal_example_code_help", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Format.
        /// </summary>
        public static string modal_format {
            get {
                return ResourceManager.GetString("modal_format", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Format: prefix + 2 digits + 3 letters.
        /// </summary>
        public static string modal_format_explanation {
            get {
                return ResourceManager.GetString("modal_format_explanation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Format help.
        /// </summary>
        public static string modal_format_help {
            get {
                return ResourceManager.GetString("modal_format_help", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Generating....
        /// </summary>
        public static string modal_generating {
            get {
                return ResourceManager.GetString("modal_generating", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à You can generate up to 20 coupons at once.
        /// </summary>
        public static string modal_max_coupons_message {
            get {
                return ResourceManager.GetString("modal_max_coupons_message", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Maximum possible number:.
        /// </summary>
        public static string modal_max_possible_number {
            get {
                return ResourceManager.GetString("modal.max_possible_number", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Number of coupons to generate.
        /// </summary>
        public static string modal_number_of_coupons {
            get {
                return ResourceManager.GetString("modal_number_of_coupons", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Prefix.
        /// </summary>
        public static string modal_prefix {
            get {
                return ResourceManager.GetString("modal_prefix", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à The prefix must contain at least 2 characters.
        /// </summary>
        public static string modal_prefix_min_length {
            get {
                return ResourceManager.GetString("modal_prefix_min_length", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Start date.
        /// </summary>
        public static string modal_start_date {
            get {
                return ResourceManager.GetString("modal_start_date", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à The start date must be today or in the future.
        /// </summary>
        public static string modal_start_date_error {
            get {
                return ResourceManager.GetString("modal_start_date_error", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à coupons have been successfully generated and saved in the database..
        /// </summary>
        public static string modal_success_message {
            get {
                return ResourceManager.GetString("modal_success_message", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Promotional coupons successfully generated for.
        /// </summary>
        public static string modal_success_title {
            get {
                return ResourceManager.GetString("modal_success_title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Edit section.
        /// </summary>
        public static string modal_title_edit_section {
            get {
                return ResourceManager.GetString("modal_title_edit_section", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à The maximum number of possible coupons exceeds 2000. If you generate more than 2000 coupons, it will take longer..
        /// </summary>
        public static string modal_warning_too_many_coupons {
            get {
                return ResourceManager.GetString("modal.warning_too_many_coupons", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Create a new module.
        /// </summary>
        public static string new_module {
            get {
                return ResourceManager.GetString("new-module", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à New Partner.
        /// </summary>
        public static string new_partner {
            get {
                return ResourceManager.GetString("new_partner", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à New Secret Key.
        /// </summary>
        public static string new_secret_key {
            get {
                return ResourceManager.GetString("new_secret_key", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à New section.
        /// </summary>
        public static string new_section {
            get {
                return ResourceManager.GetString("new_section", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à New translation.
        /// </summary>
        public static string new_translation {
            get {
                return ResourceManager.GetString("new_translation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à New variable.
        /// </summary>
        public static string new_variable {
            get {
                return ResourceManager.GetString("new_variable", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à No structures.
        /// </summary>
        public static string no_partner_structures {
            get {
                return ResourceManager.GetString("no_partner_structures", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à No associated partners.
        /// </summary>
        public static string no_partners_associated {
            get {
                return ResourceManager.GetString("no_partners_associated", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à No partners found matching your search.
        /// </summary>
        public static string no_partners_found_search {
            get {
                return ResourceManager.GetString("no_partners_found_search", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à This structure has no associated partners.
        /// </summary>
        public static string no_partners_in_structure {
            get {
                return ResourceManager.GetString("no_partners_in_structure", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à You don&apos;t have write permissions.
        /// </summary>
        public static string no_permission_write {
            get {
                return ResourceManager.GetString("no_permission_write", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à No buyer profile available..
        /// </summary>
        public static string no_profil_message {
            get {
                return ResourceManager.GetString("no_profil_message", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à No linked profiles found.
        /// </summary>
        public static string no_profils_lies_found {
            get {
                return ResourceManager.GetString("no_profils_lies_found", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à No linked profiles found in the workflow..
        /// </summary>
        public static string no_profils_lies_workflow_explanation {
            get {
                return ResourceManager.GetString("no_profils_lies_workflow_explanation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à .
        /// </summary>
        public static string no_revendeurs_database_issue {
            get {
                return ResourceManager.GetString("no_revendeurs_database_issue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à No resellers found.
        /// </summary>
        public static string no_revendeurs_found {
            get {
                return ResourceManager.GetString("no_revendeurs_found", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à No roles.
        /// </summary>
        public static string no_roles {
            get {
                return ResourceManager.GetString("no_roles", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à No structures.
        /// </summary>
        public static string no_structures {
            get {
                return ResourceManager.GetString("no_structures", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à No structures found.
        /// </summary>
        public static string no_structures_found {
            get {
                return ResourceManager.GetString("no_structures_found", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à The number of coupons.
        /// </summary>
        public static string nombre_coupons_range {
            get {
                return ResourceManager.GetString("nombre_coupons_range", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à The number of coupons is required..
        /// </summary>
        public static string nombre_coupons_required {
            get {
                return ResourceManager.GetString("nombre_coupons_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Not Blocked.
        /// </summary>
        public static string not_blocked {
            get {
                return ResourceManager.GetString("not_blocked", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Click on &apos;Check again&apos; to verify the status of the passages before cleaning..
        /// </summary>
        public static string on_initialized_click_to_verify_message {
            get {
                return ResourceManager.GetString("on_initialized_click_to_verify_message", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à  Next.
        /// </summary>
        public static string pagination_button_next {
            get {
                return ResourceManager.GetString("pagination_button_next", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Previous.
        /// </summary>
        public static string pagination_button_previous {
            get {
                return ResourceManager.GetString("pagination_button_previous", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Items per page.
        /// </summary>
        public static string pagination_items_per_page_label {
            get {
                return ResourceManager.GetString("pagination_items_per_page_label", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Pagination navigation.
        /// </summary>
        public static string pagination_navigation_label {
            get {
                return ResourceManager.GetString("pagination_navigation_label", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Page {0} of {1}.
        /// </summary>
        public static string pagination_page_info_text {
            get {
                return ResourceManager.GetString("pagination_page_info_text", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Showing items {0} to {1} of {2}.
        /// </summary>
        public static string pagination_showing_items_range {
            get {
                return ResourceManager.GetString("pagination_showing_items_range", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Order Payment.
        /// </summary>
        public static string paiement_commande {
            get {
                return ResourceManager.GetString("paiement_commande", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Enable asynchronous processing.
        /// </summary>
        public static string paiement_comment_activation_asynchrone_xml {
            get {
                return ResourceManager.GetString("paiement_comment_activation_asynchrone_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Enable multiple payments.
        /// </summary>
        public static string paiement_comment_activation_paiement_multiple_xml {
            get {
                return ResourceManager.GetString("paiement_comment_activation_paiement_multiple_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Email template activation.
        /// </summary>
        public static string paiement_comment_activation_template_mail_xml {
            get {
                return ResourceManager.GetString("paiement_comment_activation_template_mail_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Payment capture day.
        /// </summary>
        public static string paiement_comment_capture_day_xml {
            get {
                return ResourceManager.GetString("paiement_comment_capture_day_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Currency used in the transaction.
        /// </summary>
        public static string paiement_comment_currency_xml {
            get {
                return ResourceManager.GetString("paiement_comment_currency_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Disable attachment sending (PDF).
        /// </summary>
        public static string paiement_comment_desactiver_envoi_piece_jointe_pdf_xml {
            get {
                return ResourceManager.GetString("paiement_comment_desactiver_envoi_piece_jointe_pdf_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Enable document edition.
        /// </summary>
        public static string paiement_comment_doe_edition_xml {
            get {
                return ResourceManager.GetString("paiement_comment_doe_edition_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Payment channel.
        /// </summary>
        public static string paiement_comment_filieren_payment_xml {
            get {
                return ResourceManager.GetString("paiement_comment_filieren_payment_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à SP+ Merchant ID.
        /// </summary>
        public static string paiement_comment_id_marchand_spplus_xml {
            get {
                return ResourceManager.GetString("paiement_comment_id_marchand_spplus_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Minimum amount for number of payments.
        /// </summary>
        public static string paiement_comment_montant_min_nb_payment_xml {
            get {
                return ResourceManager.GetString("paiement_comment_montant_min_nb_payment_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Number of payments allowed.
        /// </summary>
        public static string paiement_comment_nb_payment_xml {
            get {
                return ResourceManager.GetString("paiement_comment_nb_payment_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Not asynchronous for buyer profile.
        /// </summary>
        public static string paiement_comment_not_asynchrone_pa_xml {
            get {
                return ResourceManager.GetString("paiement_comment_not_asynchrone_pa_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Operator used for creating the order.
        /// </summary>
        public static string paiement_comment_operateur_creation_commande_xml {
            get {
                return ResourceManager.GetString("paiement_comment_operateur_creation_commande_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Payment period.
        /// </summary>
        public static string paiement_comment_periode_xml {
            get {
                return ResourceManager.GetString("paiement_comment_periode_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Return URL for subscription.
        /// </summary>
        public static string paiement_comment_url_retour_abo_xml {
            get {
                return ResourceManager.GetString("paiement_comment_url_retour_abo_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Return URL in case of error for subscription.
        /// </summary>
        public static string paiement_comment_url_retour_err_abo_xml {
            get {
                return ResourceManager.GetString("paiement_comment_url_retour_err_abo_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Return URL in case of error for a group.
        /// </summary>
        public static string paiement_comment_url_retour_err_groupe_xml {
            get {
                return ResourceManager.GetString("paiement_comment_url_retour_err_groupe_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Return URL in case of error during the sale.
        /// </summary>
        public static string paiement_comment_url_retour_err_vente_xml {
            get {
                return ResourceManager.GetString("paiement_comment_url_retour_err_vente_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Return URL in case of error.
        /// </summary>
        public static string paiement_comment_url_retour_err_xml {
            get {
                return ResourceManager.GetString("paiement_comment_url_retour_err_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Return URL for a group.
        /// </summary>
        public static string paiement_comment_url_retour_groupe_xml {
            get {
                return ResourceManager.GetString("paiement_comment_url_retour_groupe_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Return URL after a sale.
        /// </summary>
        public static string paiement_comment_url_retour_vente_xml {
            get {
                return ResourceManager.GetString("paiement_comment_url_retour_vente_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Return URL for subscription cancellation.
        /// </summary>
        public static string paiement_comment_url_retourannul_abo_xml {
            get {
                return ResourceManager.GetString("paiement_comment_url_retourannul_abo_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Return URL for a canceled group.
        /// </summary>
        public static string paiement_comment_url_retourannul_groupe_xml {
            get {
                return ResourceManager.GetString("paiement_comment_url_retourannul_groupe_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Return URL for a canceled sal.
        /// </summary>
        public static string paiement_comment_url_retourannul_vente_xml {
            get {
                return ResourceManager.GetString("paiement_comment_url_retourannul_vente_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à URL of the payment site.
        /// </summary>
        public static string paiement_comment_urls_site_xml {
            get {
                return ResourceManager.GetString("paiement_comment_urls_site_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Payment application version.
        /// </summary>
        public static string paiement_comment_version_xml {
            get {
                return ResourceManager.GetString("paiement_comment_version_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Web operator ID.
        /// </summary>
        public static string paiement_comment_web_operateur_id_xml {
            get {
                return ResourceManager.GetString("paiement_comment_web_operateur_id_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Web post ID.
        /// </summary>
        public static string paiement_comment_web_post_id_xml {
            get {
                return ResourceManager.GetString("paiement_comment_web_post_id_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à The expiration time ({0}) cannot be less than the deflagration value ({1})..
        /// </summary>
        public static string panier_expiration_inferieure_deflag {
            get {
                return ResourceManager.GetString("panier_expiration_inferieure_deflag", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Banner name.
        /// </summary>
        public static string param_comment_bannername_xml {
            get {
                return ResourceManager.GetString("param_comment_bannername_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Gift vouchers available.
        /// </summary>
        public static string param_comment_bonscadeaux_xml {
            get {
                return ResourceManager.GetString("param_comment_bonscadeaux_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Position of the currency (before or after the amount).
        /// </summary>
        public static string param_comment_devise_before_xml {
            get {
                return ResourceManager.GetString("param_comment_devise_before_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Currency code.
        /// </summary>
        public static string param_comment_devise_code_xml {
            get {
                return ResourceManager.GetString("param_comment_devise_code_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à ISO code of the currency.
        /// </summary>
        public static string param_comment_devise_iso_xml {
            get {
                return ResourceManager.GetString("param_comment_devise_iso_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Currency separator.
        /// </summary>
        public static string param_comment_devise_separator_xml {
            get {
                return ResourceManager.GetString("param_comment_devise_separator_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Banner extension (take the first one found).
        /// </summary>
        public static string param_comment_extensionbanner_xml {
            get {
                return ResourceManager.GetString("param_comment_extensionbanner_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Blocking threshold.
        /// </summary>
        public static string param_comment_facturemode_seuildeblocage_xml {
            get {
                return ResourceManager.GetString("param_comment_facturemode_seuildeblocage_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à File fees for free amount.
        /// </summary>
        public static string param_comment_fraisdossiermontantgratuit_xml {
            get {
                return ResourceManager.GetString("param_comment_fraisdossiermontantgratuit_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Manifestation group excluded from widgets.
        /// </summary>
        public static string param_comment_groupe_manif_exclude_widget_xml {
            get {
                return ResourceManager.GetString("param_comment_groupe_manif_exclude_widget_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Manage insurance contract, yes=1.
        /// </summary>
        public static string param_comment_insurance_xml {
            get {
                return ResourceManager.GetString("param_comment_insurance_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à German language.
        /// </summary>
        public static string param_comment_langue_de_xml {
            get {
                return ResourceManager.GetString("param_comment_langue_de_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à English language.
        /// </summary>
        public static string param_comment_langue_en_xml {
            get {
                return ResourceManager.GetString("param_comment_langue_en_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Italian language.
        /// </summary>
        public static string param_comment_langue_it_xml {
            get {
                return ResourceManager.GetString("param_comment_langue_it_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Spanish language.
        /// </summary>
        public static string param_comment_langue_sp_xml {
            get {
                return ResourceManager.GetString("param_comment_langue_sp_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Time before payment (in minutes).
        /// </summary>
        public static string param_comment_minutestogotopaiement_xml {
            get {
                return ResourceManager.GetString("param_comment_minutestogotopaiement_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Multi-language activation.
        /// </summary>
        public static string param_comment_multilangue_xml {
            get {
                return ResourceManager.GetString("param_comment_multilangue_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Payment provider.
        /// </summary>
        public static string param_comment_prestataire_paiement_xml {
            get {
                return ResourceManager.GetString("param_comment_prestataire_paiement_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Display only one date.
        /// </summary>
        public static string param_comment_showonlyonedate_xml {
            get {
                return ResourceManager.GetString("param_comment_showonlyonedate_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Use customer area.
        /// </summary>
        public static string param_comment_usecustomerarea_xml {
            get {
                return ResourceManager.GetString("param_comment_usecustomerarea_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Language parameter for configuration.
        /// </summary>
        public static string param_langue_com_configini_xml {
            get {
                return ResourceManager.GetString("param_langue_com_configini_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à User password.
        /// </summary>
        public static string paramprintathome_comment_ticketeos_credpassw_xml {
            get {
                return ResourceManager.GetString("paramprintathome_comment_ticketeos_credpassw_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à User credential.
        /// </summary>
        public static string paramprintathome_comment_ticketeos_creduser_xml {
            get {
                return ResourceManager.GetString("paramprintathome_comment_ticketeos_creduser_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Organizer ID.
        /// </summary>
        public static string paramprintathome_comment_ticketeos_organizerid_xml {
            get {
                return ResourceManager.GetString("paramprintathome_comment_ticketeos_organizerid_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à System ID.
        /// </summary>
        public static string paramprintathome_comment_ticketeos_systemid_xml {
            get {
                return ResourceManager.GetString("paramprintathome_comment_ticketeos_systemid_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Token.
        /// </summary>
        public static string paramprintathome_comment_ticketeos_token_xml {
            get {
                return ResourceManager.GetString("paramprintathome_comment_ticketeos_token_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à KombiTicket URL.
        /// </summary>
        public static string paramprintathome_comment_ticketeos_urlkombiticket_xml {
            get {
                return ResourceManager.GetString("paramprintathome_comment_ticketeos_urlkombiticket_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Parent translation key.
        /// </summary>
        public static string parent_translation_key {
            get {
                return ResourceManager.GetString("parent_translation_key", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Secret key.
        /// </summary>
        public static string partener_secret_key {
            get {
                return ResourceManager.GetString("partener_secret_key", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Partner Management.
        /// </summary>
        public static string partners {
            get {
                return ResourceManager.GetString("partners", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à partner(s).
        /// </summary>
        public static string partners_count {
            get {
                return ResourceManager.GetString("partners_count", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à partner(s) found.
        /// </summary>
        public static string partners_found {
            get {
                return ResourceManager.GetString("partners_found", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Partners of.
        /// </summary>
        public static string partners_of_structure {
            get {
                return ResourceManager.GetString("partners_of_structure", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Translation of the payment platform.
        /// </summary>
        public static string payment_platform_translation {
            get {
                return ResourceManager.GetString("payment_platform_translation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Access token.
        /// </summary>
        public static string paypalapi_comment_accesstoken_xml {
            get {
                return ResourceManager.GetString("paypalapi_comment_accesstoken_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Currency.
        /// </summary>
        public static string paypalapi_comment_currency_xml {
            get {
                return ResourceManager.GetString("paypalapi_comment_currency_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Mode.
        /// </summary>
        public static string paypalapi_comment_mode_xml {
            get {
                return ResourceManager.GetString("paypalapi_comment_mode_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Password for PayPal Connect.
        /// </summary>
        public static string paypalconnect_comment_password_xml {
            get {
                return ResourceManager.GetString("paypalconnect_comment_password_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Username for PayPal Connect.
        /// </summary>
        public static string paypalconnect_comment_username_xml {
            get {
                return ResourceManager.GetString("paypalconnect_comment_username_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Accepted language.
        /// </summary>
        public static string pelecard_comment_acceptedlanguage_xml_ {
            get {
                return ResourceManager.GetString("pelecard_comment_acceptedlanguage_xml ", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Action Type.
        /// </summary>
        public static string pelecard_comment_actiontype_xml {
            get {
                return ResourceManager.GetString("pelecard_comment_actiontype_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Currency.
        /// </summary>
        public static string pelecard_comment_currency_xml {
            get {
                return ResourceManager.GetString("pelecard_comment_currency_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Login ID.
        /// </summary>
        public static string pelecard_comment_login_xml {
            get {
                return ResourceManager.GetString("pelecard_comment_login_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Password.
        /// </summary>
        public static string pelecard_comment_password_xml {
            get {
                return ResourceManager.GetString("pelecard_comment_password_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Terminal ID.
        /// </summary>
        public static string pelecard_comment_terminalid_xml {
            get {
                return ResourceManager.GetString("pelecard_comment_terminalid_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à URL.
        /// </summary>
        public static string pelecard_comment_url_xml {
            get {
                return ResourceManager.GetString("pelecard_comment_url_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Back to Home.
        /// </summary>
        public static string popup_save_success_button {
            get {
                return ResourceManager.GetString("popup_save_success_button", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Save modifications successfully, Well Done!.
        /// </summary>
        public static string popup_save_success_message {
            get {
                return ResourceManager.GetString("popup_save_success_message", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à  Prep for sale.
        /// </summary>
        public static string preparation_mise_vente {
            get {
                return ResourceManager.GetString("preparation-mise-vente", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à  Available Actions.
        /// </summary>
        public static string preparation_mise_vente_actions {
            get {
                return ResourceManager.GetString("preparation_mise_vente_actions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Wait until the recent passages are more than one hour old..
        /// </summary>
        public static string preparation_mise_vente_advice_wait_or_disconnect {
            get {
                return ResourceManager.GetString("preparation_mise_vente_advice_wait_or_disconnect", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Cancel.
        /// </summary>
        public static string preparation_mise_vente_cancel_button {
            get {
                return ResourceManager.GetString("preparation_mise_vente_cancel_button", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à  Check Again.
        /// </summary>
        public static string preparation_mise_vente_check_button {
            get {
                return ResourceManager.GetString("preparation_mise_vente_check_button", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à  Refreshes verification status.
        /// </summary>
        public static string preparation_mise_vente_check_button_hin {
            get {
                return ResourceManager.GetString("preparation_mise_vente_check_button_hin", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Refreshes verification status.
        /// </summary>
        public static string preparation_mise_vente_check_button_hint {
            get {
                return ResourceManager.GetString("preparation_mise_vente_check_button_hint", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Error during verification: {0}.
        /// </summary>
        public static string preparation_mise_vente_check_error {
            get {
                return ResourceManager.GetString("preparation_mise_vente_check_error", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à No recent passage detected. Cleaning can be performed safely..
        /// </summary>
        public static string preparation_mise_vente_check_success {
            get {
                return ResourceManager.GetString("preparation_mise_vente_check_success", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Warning: Recent data (less than 1 hour) detected. Cleanup not possible..
        /// </summary>
        public static string preparation_mise_vente_check_warning {
            get {
                return ResourceManager.GetString("preparation_mise_vente_check_warning", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Cleanup Blocked.
        /// </summary>
        public static string preparation_mise_vente_cleanup_blocked {
            get {
                return ResourceManager.GetString("preparation_mise_vente_cleanup_blocked", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Clean the Tables.
        /// </summary>
        public static string preparation_mise_vente_cleanup_button {
            get {
                return ResourceManager.GetString("preparation_mise_vente_cleanup_button", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Deletes all data from queuing tables.
        /// </summary>
        public static string preparation_mise_vente_cleanup_button_hint {
            get {
                return ResourceManager.GetString("preparation_mise_vente_cleanup_button_hint", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Cleanup Confirmation.
        /// </summary>
        public static string preparation_mise_vente_cleanup_confirmation_title {
            get {
                return ResourceManager.GetString("preparation_mise_vente_cleanup_confirmation_title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Wait until the recent passages are more than one hour old or until the users are no longer connected..
        /// </summary>
        public static string preparation_mise_vente_cleanup_irreversible {
            get {
                return ResourceManager.GetString("preparation_mise_vente_cleanup_irreversible", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Verification Required.
        /// </summary>
        public static string preparation_mise_vente_cleanup_required {
            get {
                return ResourceManager.GetString("preparation_mise_vente_cleanup_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Cleanup will delete all data from queuing_{0} and queuing_{0}_histopassages..
        /// </summary>
        public static string preparation_mise_vente_cleanup_warning {
            get {
                return ResourceManager.GetString("preparation_mise_vente_cleanup_warning", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Click on &apos;Check again&apos; to verify the status of the passages before cleaning..
        /// </summary>
        public static string preparation_mise_vente_click_verify {
            get {
                return ResourceManager.GetString("preparation_mise_vente_click_verify", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Cleanup could not be performed. Recent data is present..
        /// </summary>
        public static string preparation_mise_vente_error_message {
            get {
                return ResourceManager.GetString("preparation_mise_vente_error_message", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Checking data in progress....
        /// </summary>
        public static string preparation_mise_vente_loading {
            get {
                return ResourceManager.GetString("preparation_mise_vente_loading", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à No recent passage detected..
        /// </summary>
        public static string preparation_mise_vente_no_recent_data {
            get {
                return ResourceManager.GetString("preparation_mise_vente_no_recent_data", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Not verified.
        /// </summary>
        public static string preparation_mise_vente_not_verified {
            get {
                return ResourceManager.GetString("preparation_mise_vente_not_verified", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à The status of the passages has not been checked yet..
        /// </summary>
        public static string preparation_mise_vente_not_verified_description {
            get {
                return ResourceManager.GetString("preparation_mise_vente_not_verified_description", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Click &apos;Check again&apos; to check the data..
        /// </summary>
        public static string preparation_mise_vente_not_verified_hint {
            get {
                return ResourceManager.GetString("preparation_mise_vente_not_verified_hint", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à  Ready for Cleanup.
        /// </summary>
        public static string preparation_mise_vente_ready_to_cleanup {
            get {
                return ResourceManager.GetString("preparation_mise_vente_ready_to_cleanup", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Recent passages detected.
        /// </summary>
        public static string preparation_mise_vente_recent_data {
            get {
                return ResourceManager.GetString("preparation_mise_vente_recent_data", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Passages less than one hour old are present in the queuing table..
        /// </summary>
        public static string preparation_mise_vente_recent_data_description {
            get {
                return ResourceManager.GetString("preparation_mise_vente_recent_data_description", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Cleanup is currently blocked for safety reasons..
        /// </summary>
        public static string preparation_mise_vente_recent_data_hint {
            get {
                return ResourceManager.GetString("preparation_mise_vente_recent_data_hint", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Table cleanup can be safely performed..
        /// </summary>
        public static string preparation_mise_vente_safe_to_cleanup {
            get {
                return ResourceManager.GetString("preparation_mise_vente_safe_to_cleanup", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Structure: {0} ({1}).
        /// </summary>
        public static string preparation_mise_vente_structure_label {
            get {
                return ResourceManager.GetString("preparation_mise_vente_structure_label", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Tables were successfully cleaned. All queuing table data has been deleted..
        /// </summary>
        public static string preparation_mise_vente_success_message {
            get {
                return ResourceManager.GetString("preparation_mise_vente_success_message", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à  Sale Preparation.
        /// </summary>
        public static string preparation_mise_vente_title {
            get {
                return ResourceManager.GetString("preparation_mise_vente_title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Verification Status.
        /// </summary>
        public static string preparation_mise_vente_verification_status {
            get {
                return ResourceManager.GetString("preparation_mise_vente_verification_status", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Please check the status of the passages first before proceeding with the cleaning..
        /// </summary>
        public static string preparation_mise_vente_verify_first {
            get {
                return ResourceManager.GetString("preparation_mise_vente_verify_first", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à This check ensures no recent data will be lost..
        /// </summary>
        public static string preparation_mise_vente_verify_purpose {
            get {
                return ResourceManager.GetString("preparation_mise_vente_verify_purpose", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à PREPROD Environment.
        /// </summary>
        public static string preprod_environment {
            get {
                return ResourceManager.GetString("preprod_environment", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Multizone sale.
        /// </summary>
        public static string prisepacessurplan_comment_ismultizones_xml {
            get {
                return ResourceManager.GetString("prisepacessurplan_comment_ismultizones_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à PROD Environment.
        /// </summary>
        public static string prod_environment {
            get {
                return ResourceManager.GetString("prod_environment", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Buyer Profile.
        /// </summary>
        public static string profil_acheteur {
            get {
                return ResourceManager.GetString("profil_acheteur", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Invalid buyer profile..
        /// </summary>
        public static string profil_acheteur_non_valide {
            get {
                return ResourceManager.GetString("profil_acheteur_non_valide", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à File status for subscription check.
        /// </summary>
        public static string reabo_comment_dossieretatcheckabo_xml {
            get {
                return ResourceManager.GetString("reabo_comment_dossieretatcheckabo_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Subscription renewal formula.
        /// </summary>
        public static string reabo_comment_formule_xml {
            get {
                return ResourceManager.GetString("reabo_comment_formule_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Prohibit complementary info update for fans.
        /// </summary>
        public static string reabo_comment_interdireinfocompupdatefans_xml {
            get {
                return ResourceManager.GetString("reabo_comment_interdireinfocompupdatefans_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Update fans on structure.
        /// </summary>
        public static string reabo_comment_updatefansonstructure_xml {
            get {
                return ResourceManager.GetString("reabo_comment_updatefansonstructure_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Remaining time.
        /// </summary>
        public static string remaining_time {
            get {
                return ResourceManager.GetString("remaining_time", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Restore.
        /// </summary>
        public static string restore_action {
            get {
                return ResourceManager.GetString("restore_action", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à  Restore the last file.
        /// </summary>
        public static string restore_last_file_button_btnSecondary {
            get {
                return ResourceManager.GetString("restore_last_file_button_btnSecondary", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Back.
        /// </summary>
        public static string retour_liste_struture {
            get {
                return ResourceManager.GetString("retour_liste_struture", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Return to partners.
        /// </summary>
        public static string return_to_partners {
            get {
                return ResourceManager.GetString("return_to_partners", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Reseller.
        /// </summary>
        public static string revendeur {
            get {
                return ResourceManager.GetString("revendeur", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Event duration in minutes for reseller (configurable request).
        /// </summary>
        public static string revendeur_comment_duration_xml {
            get {
                return ResourceManager.GetString("revendeur_comment_duration_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Message for download from Fast Track.
        /// </summary>
        public static string revendeur_comment_message_commentaire_xml {
            get {
                return ResourceManager.GetString("revendeur_comment_message_commentaire_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Password for real-time interface with Ticketac.
        /// </summary>
        public static string revendeur_comment_ticketacapi_passw_xml {
            get {
                return ResourceManager.GetString("revendeur_comment_ticketacapi_passw_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à URL for real-time interface with Ticketac vendor.
        /// </summary>
        public static string revendeur_comment_ticketacapi_url_xml {
            get {
                return ResourceManager.GetString("revendeur_comment_ticketacapi_url_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à User ID for real-time interface with Ticketac.
        /// </summary>
        public static string revendeur_comment_ticketacapi_user_xml {
            get {
                return ResourceManager.GetString("revendeur_comment_ticketacapi_user_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Reseller from structure.
        /// </summary>
        public static string revendeur_from_structure {
            get {
                return ResourceManager.GetString("revendeur_from_structure", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Reseller buyer profile.
        /// </summary>
        public static string revendeur_profil_acheteur {
            get {
                return ResourceManager.GetString("revendeur_profil_acheteur", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Reseller structure.
        /// </summary>
        public static string revendeur_structure {
            get {
                return ResourceManager.GetString("revendeur_structure", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Create a new role.
        /// </summary>
        public static string role_create_new {
            get {
                return ResourceManager.GetString("role_create_new", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Role Management.
        /// </summary>
        public static string role_management {
            get {
                return ResourceManager.GetString("role-management", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Actions.
        /// </summary>
        public static string rolemanagement_actions {
            get {
                return ResourceManager.GetString("rolemanagement_actions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à AD Groups.
        /// </summary>
        public static string rolemanagement_ad_groups {
            get {
                return ResourceManager.GetString("rolemanagement_ad_groups", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Assign.
        /// </summary>
        public static string rolemanagement_assign {
            get {
                return ResourceManager.GetString("rolemanagement_assign", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Role Assignment.
        /// </summary>
        public static string rolemanagement_assign_roles {
            get {
                return ResourceManager.GetString("rolemanagement_assign_roles", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Cancel.
        /// </summary>
        public static string rolemanagement_cancel {
            get {
                return ResourceManager.GetString("rolemanagement_cancel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Can Create.
        /// </summary>
        public static string rolemanagement_cancreate {
            get {
                return ResourceManager.GetString("rolemanagement_cancreate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Can Delete.
        /// </summary>
        public static string rolemanagement_candelete {
            get {
                return ResourceManager.GetString("rolemanagement_candelete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Can Read.
        /// </summary>
        public static string rolemanagement_canread {
            get {
                return ResourceManager.GetString("rolemanagement_canread", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Can Update.
        /// </summary>
        public static string rolemanagement_canupdate {
            get {
                return ResourceManager.GetString("rolemanagement_canupdate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Choose option.
        /// </summary>
        public static string rolemanagement_choose_option {
            get {
                return ResourceManager.GetString("rolemanagement_choose_option", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Create a group.
        /// </summary>
        public static string rolemanagement_create_group {
            get {
                return ResourceManager.GetString("rolemanagement_create_group", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Create a Module.
        /// </summary>
        public static string rolemanagement_create_module {
            get {
                return ResourceManager.GetString("rolemanagement_create_module", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Create a new role.
        /// </summary>
        public static string rolemanagement_create_role {
            get {
                return ResourceManager.GetString("rolemanagement_create_role", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Delete.
        /// </summary>
        public static string rolemanagement_delete {
            get {
                return ResourceManager.GetString("rolemanagement_delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à An error occurred while deleting: {0}.
        /// </summary>
        public static string rolemanagement_delete_error_message {
            get {
                return ResourceManager.GetString("rolemanagement_delete_error_message", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à  Error during deletion.
        /// </summary>
        public static string rolemanagement_delete_error_title {
            get {
                return ResourceManager.GetString("rolemanagement_delete_error_title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à The module was successfully deleted..
        /// </summary>
        public static string rolemanagement_delete_success_messag {
            get {
                return ResourceManager.GetString("rolemanagement_delete_success_messag", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Deletion successful.
        /// </summary>
        public static string rolemanagement_delete_success_title {
            get {
                return ResourceManager.GetString("rolemanagement_delete_success_title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Edit module.
        /// </summary>
        public static string rolemanagement_edit {
            get {
                return ResourceManager.GetString("rolemanagement_edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à An error occurred while editing: {0}.
        /// </summary>
        public static string rolemanagement_edit_error_message {
            get {
                return ResourceManager.GetString("rolemanagement_edit_error_message", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à  Error during editing.
        /// </summary>
        public static string rolemanagement_edit_error_title {
            get {
                return ResourceManager.GetString("rolemanagement_edit_error_title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à  Edit module.
        /// </summary>
        public static string rolemanagement_edit_module {
            get {
                return ResourceManager.GetString("rolemanagement_edit_module", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à The module was successfully edited..
        /// </summary>
        public static string rolemanagement_edit_success_message {
            get {
                return ResourceManager.GetString("rolemanagement_edit_success_message", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Edit successful.
        /// </summary>
        public static string rolemanagement_edit_success_title {
            get {
                return ResourceManager.GetString("rolemanagement_edit_success_title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Group ID.
        /// </summary>
        public static string rolemanagement_groupid {
            get {
                return ResourceManager.GetString("rolemanagement_groupid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Group Name.
        /// </summary>
        public static string rolemanagement_groupname {
            get {
                return ResourceManager.GetString("rolemanagement_groupname", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Level.
        /// </summary>
        public static string rolemanagement_level {
            get {
                return ResourceManager.GetString("rolemanagement_level", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Please wait while loading data....
        /// </summary>
        public static string rolemanagement_loading {
            get {
                return ResourceManager.GetString("rolemanagement_loading", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Module.
        /// </summary>
        public static string rolemanagement_module {
            get {
                return ResourceManager.GetString("rolemanagement_module", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Module comment.
        /// </summary>
        public static string rolemanagement_module_comment {
            get {
                return ResourceManager.GetString("rolemanagement_module_comment", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à  Module name.
        /// </summary>
        public static string rolemanagement_module_name {
            get {
                return ResourceManager.GetString("rolemanagement_module_name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Comment.
        /// </summary>
        public static string rolemanagement_modulecomment {
            get {
                return ResourceManager.GetString("rolemanagement_modulecomment", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Module Name.
        /// </summary>
        public static string rolemanagement_modulename {
            get {
                return ResourceManager.GetString("rolemanagement_modulename", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Modules.
        /// </summary>
        public static string rolemanagement_modules {
            get {
                return ResourceManager.GetString("rolemanagement_modules", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à No Role.
        /// </summary>
        public static string rolemanagement_no_module {
            get {
                return ResourceManager.GetString("rolemanagement_no_module", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à No Module or Role for {0}.
        /// </summary>
        public static string rolemanagement_no_module_or_role {
            get {
                return ResourceManager.GetString("rolemanagement_no_module_or_role", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Role.
        /// </summary>
        public static string rolemanagement_role {
            get {
                return ResourceManager.GetString("rolemanagement_role", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Role Name.
        /// </summary>
        public static string rolemanagement_rolename {
            get {
                return ResourceManager.GetString("rolemanagement_rolename", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Roles.
        /// </summary>
        public static string rolemanagement_roles {
            get {
                return ResourceManager.GetString("rolemanagement_roles", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Save.
        /// </summary>
        public static string rolemanagement_save {
            get {
                return ResourceManager.GetString("rolemanagement_save", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Role and Module Management.
        /// </summary>
        public static string rolemanagement_title {
            get {
                return ResourceManager.GetString("rolemanagement_title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Enter the time before cart expiration (1-1430 minutes)..
        /// </summary>
        public static string Saisir_Temps_Expiration {
            get {
                return ResourceManager.GetString("Saisir_Temps_Expiration", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Save.
        /// </summary>
        public static string save_button {
            get {
                return ResourceManager.GetString("save_button", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Save Link.
        /// </summary>
        public static string save_liaison {
            get {
                return ResourceManager.GetString("save_liaison", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Saving....
        /// </summary>
        public static string saving {
            get {
                return ResourceManager.GetString("saving", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Search and select a profile.
        /// </summary>
        public static string search_and_select_profil {
            get {
                return ResourceManager.GetString("search_and_select_profil", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Search.
        /// </summary>
        public static string search_button {
            get {
                return ResourceManager.GetString("search_button", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Search by keys.
        /// </summary>
        public static string search_by_key_fieldName {
            get {
                return ResourceManager.GetString("search_by_key_fieldName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Search by keyword.
        /// </summary>
        public static string search_by_keyword {
            get {
                return ResourceManager.GetString("search_by_keyword", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Enter a key....
        /// </summary>
        public static string search_key_input_placeholder {
            get {
                return ResourceManager.GetString("search_key_input_placeholder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Missing translation.
        /// </summary>
        public static string search_off_missing_translation_notice {
            get {
                return ResourceManager.GetString("search_off_missing_translation_notice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à No translation found.
        /// </summary>
        public static string search_off_no_translation_found_title {
            get {
                return ResourceManager.GetString("search_off_no_translation_found_title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Try changing your search criteria..
        /// </summary>
        public static string search_off_try_changing_search_criteria_message {
            get {
                return ResourceManager.GetString("search_off_try_changing_search_criteria_message", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Enter partner name....
        /// </summary>
        public static string search_partner_placeholder {
            get {
                return ResourceManager.GetString("search_partner_placeholder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Search partners in this structure.
        /// </summary>
        public static string search_partners_in_structure {
            get {
                return ResourceManager.GetString("search_partners_in_structure", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Search structures....
        /// </summary>
        public static string search_structures {
            get {
                return ResourceManager.GetString("search_structures", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Section name.
        /// </summary>
        public static string section_name {
            get {
                return ResourceManager.GetString("section_name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Select.
        /// </summary>
        public static string select {
            get {
                return ResourceManager.GetString("select", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à All sections.
        /// </summary>
        public static string select_all_sections {
            get {
                return ResourceManager.GetString("select_all_sections", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Select a language.
        /// </summary>
        public static string select_language {
            get {
                return ResourceManager.GetString("select_language", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Select a parent key.
        /// </summary>
        public static string select_parent_key {
            get {
                return ResourceManager.GetString("select_parent_key", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à  Select a Buyer Profile.
        /// </summary>
        public static string select_profil_acheteur {
            get {
                return ResourceManager.GetString("select_profil_acheteur", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Select a dealer .
        /// </summary>
        public static string select_revendeur {
            get {
                return ResourceManager.GetString("select_revendeur", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Reseller selection description.
        /// </summary>
        public static string select_revendeur_description {
            get {
                return ResourceManager.GetString("select_revendeur_description", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Select a section.
        /// </summary>
        public static string select_section {
            get {
                return ResourceManager.GetString("select_section", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Select a structure.
        /// </summary>
        public static string select_structure {
            get {
                return ResourceManager.GetString("select_structure", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Select a structure first.
        /// </summary>
        public static string select_structure_first {
            get {
                return ResourceManager.GetString("select_structure_first", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Select a structure.
        /// </summary>
        public static string select_structure_option {
            get {
                return ResourceManager.GetString("select_structure_option", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à -- Select a structure --.
        /// </summary>
        public static string select_structure_placeholder {
            get {
                return ResourceManager.GetString("select_structure_placeholder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Select the structure profile to link.
        /// </summary>
        public static string select_structure_profil_to_link {
            get {
                return ResourceManager.GetString("select_structure_profil_to_link", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Select a structure above to view its partners.
        /// </summary>
        public static string select_structure_to_view_partners {
            get {
                return ResourceManager.GetString("select_structure_to_view_partners", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Please select the structure.
        /// </summary>
        public static string select_target_structure_explanation {
            get {
                return ResourceManager.GetString("select_target_structure_explanation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à View empty fields of the language.
        /// </summary>
        public static string select_view_empty_fields_language {
            get {
                return ResourceManager.GetString("select_view_empty_fields_language", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Selected profile.
        /// </summary>
        public static string selected_profil {
            get {
                return ResourceManager.GetString("selected_profil", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Selected profiles for liaison.
        /// </summary>
        public static string selected_profils_for_liaison {
            get {
                return ResourceManager.GetString("selected_profils_for_liaison", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Selected reseller.
        /// </summary>
        public static string selected_revendeur {
            get {
                return ResourceManager.GetString("selected_revendeur", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Selected structure status.
        /// </summary>
        public static string selected_structure_status {
            get {
                return ResourceManager.GetString("selected_structure_status", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à structure(s) selected.
        /// </summary>
        public static string selected_structures_count {
            get {
                return ResourceManager.GetString("selected_structures_count", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à .
        /// </summary>
        public static string selected_structures_partners_title {
            get {
                return ResourceManager.GetString("selected_structures_partners_title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Selected Variables.
        /// </summary>
        public static string selected_variables {
            get {
                return ResourceManager.GetString("selected_variables", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Self-closing tag.
        /// </summary>
        public static string self_closing_tag {
            get {
                return ResourceManager.GetString("self_closing_tag", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Error creating module.
        /// </summary>
        public static string Serror_create_module_title {
            get {
                return ResourceManager.GetString("Serror_create_module_title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Blocking Status.
        /// </summary>
        public static string service_blocked_status {
            get {
                return ResourceManager.GetString("service_blocked_status", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Inclusion/Exclusion.
        /// </summary>
        public static string service_inclusion_exclusion {
            get {
                return ResourceManager.GetString("service-inclusion-exclusion", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Blocked.
        /// </summary>
        public static string service_inclusion_exclusion_blocked {
            get {
                return ResourceManager.GetString("service_inclusion_exclusion_blocked", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Service Name.
        /// </summary>
        public static string service_inclusion_exclusion_name {
            get {
                return ResourceManager.GetString("service_inclusion_exclusion_name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Blocking Status.
        /// </summary>
        public static string service_inclusion_exlusion_blocked_status {
            get {
                return ResourceManager.GetString("service_inclusion_exlusion_blocked_status", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Display of the opinion order on the payment site (1 to activate, 0 to deactivate).
        /// </summary>
        public static string services_comment_opinion_order_xml {
            get {
                return ResourceManager.GetString("services_comment_opinion_order_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Services Inclusion and Exclusion.
        /// </summary>
        public static string services_inclusion_exclusion {
            get {
                return ResourceManager.GetString("services_inclusion_exclusion", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Manage included and excluded structures for different services in PREPROD and PROD environments..
        /// </summary>
        public static string services_inclusion_exclusion_info {
            get {
                return ResourceManager.GetString("services_inclusion_exclusion_info", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Show all profiles.
        /// </summary>
        public static string show_all_profils {
            get {
                return ResourceManager.GetString("show_all_profils", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à  Show required fields.
        /// </summary>
        public static string show_mandatory_fields_label {
            get {
                return ResourceManager.GetString("show_mandatory_fields_label", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Show modified fields.
        /// </summary>
        public static string show_modified_fields_label {
            get {
                return ResourceManager.GetString("show_modified_fields_label", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Application ID.
        /// </summary>
        public static string smsefidem_comment_appid_xml {
            get {
                return ResourceManager.GetString("smsefidem_comment_appid_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Login ID.
        /// </summary>
        public static string smsefidem_comment_login_xml {
            get {
                return ResourceManager.GetString("smsefidem_comment_login_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Password.
        /// </summary>
        public static string smsefidem_comment_passw_xml {
            get {
                return ResourceManager.GetString("smsefidem_comment_passw_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Indicator to manage seats during payment.
        /// </summary>
        public static string specifique_comment_flagdesplacesdanspaiement_xml {
            get {
                return ResourceManager.GetString("specifique_comment_flagdesplacesdanspaiement_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Automatic invoice number generation.
        /// </summary>
        public static string specifique_comment_generernumerofacture_xml {
            get {
                return ResourceManager.GetString("specifique_comment_generernumerofacture_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Undefined session ID.
        /// </summary>
        public static string specifique_comment_seanceidnotset_xml {
            get {
                return ResourceManager.GetString("specifique_comment_seanceidnotset_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Optimized.
        /// </summary>
        public static string stats_optimise {
            get {
                return ResourceManager.GetString("stats_optimise", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Possibilities.
        /// </summary>
        public static string stats_possibilites {
            get {
                return ResourceManager.GetString("stats_possibilites", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à  Productivity.
        /// </summary>
        public static string stats_productivite {
            get {
                return ResourceManager.GetString("stats_productivite", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Continuous Support.
        /// </summary>
        public static string stats_support_continu {
            get {
                return ResourceManager.GetString("stats_support_continu", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Choose a Reseller.
        /// </summary>
        public static string step_1_select_revendeur {
            get {
                return ResourceManager.GetString("step_1_select_revendeur", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Profiles linked to the reseller.
        /// </summary>
        public static string step_2_profils_lies_revendeur {
            get {
                return ResourceManager.GetString("step_2_profils_lies_revendeur", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Select a Structure.
        /// </summary>
        public static string step_3_select_structure {
            get {
                return ResourceManager.GetString("step_3_select_structure", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Add missing keys.
        /// </summary>
        public static string Stradd_missing_keys {
            get {
                return ResourceManager.GetString("Stradd_missing_keys", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Structure added successfully.
        /// </summary>
        public static string structure_added_successfully {
            get {
                return ResourceManager.GetString("structure_added_successfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Structure {0} added to exclusionsStructures.xml PROD.
        /// </summary>
        public static string structure_added_to_exclusion_prod {
            get {
                return ResourceManager.GetString("structure_added_to_exclusion_prod", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Structure {0} added to inclusionsStructures.xml PREPROD.
        /// </summary>
        public static string structure_added_to_inclusion_preprod {
            get {
                return ResourceManager.GetString("structure_added_to_inclusion_preprod", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Structure already exists.
        /// </summary>
        public static string structure_already_exists {
            get {
                return ResourceManager.GetString("structure_already_exists", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Partners associated with this structure.
        /// </summary>
        public static string structure_associated_partners {
            get {
                return ResourceManager.GetString("structure_associated_partners", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Structure {0} does not have a configured or accessible database connection..
        /// </summary>
        public static string structure_database_not_configured {
            get {
                return ResourceManager.GetString("structure_database_not_configured", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Default Status.
        /// </summary>
        public static string structure_default_status {
            get {
                return ResourceManager.GetString("structure_default_status", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Structure Excluded.
        /// </summary>
        public static string structure_excluded {
            get {
                return ResourceManager.GetString("structure_excluded", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Structure ID.
        /// </summary>
        public static string structure_id {
            get {
                return ResourceManager.GetString("structure_id", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Structure Included.
        /// </summary>
        public static string structure_included {
            get {
                return ResourceManager.GetString("structure_included", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à  Error while loading structures: {0}.
        /// </summary>
        public static string structure_loading_error {
            get {
                return ResourceManager.GetString("structure_loading_error", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à  Structure name.
        /// </summary>
        public static string structure_name {
            get {
                return ResourceManager.GetString("structure_name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à  No structure was found in the database..
        /// </summary>
        public static string structure_no_data_found {
            get {
                return ResourceManager.GetString("structure_no_data_found", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Structure not found.
        /// </summary>
        public static string structure_not_found {
            get {
                return ResourceManager.GetString("structure_not_found", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Partners of Structure.
        /// </summary>
        public static string structure_partners_title {
            get {
                return ResourceManager.GetString("structure_partners_title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à .
        /// </summary>
        public static string structure_profil_acheteur {
            get {
                return ResourceManager.GetString("structure_profil_acheteur", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Structure {0} removed from exclusionsStructures.xml PROD.
        /// </summary>
        public static string structure_removed_from_exclusion_prod {
            get {
                return ResourceManager.GetString("structure_removed_from_exclusion_prod", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à  Structure {0} removed from inclusionsStructures.xml PREPROD.
        /// </summary>
        public static string structure_removed_from_inclusion_preprod {
            get {
                return ResourceManager.GetString("structure_removed_from_inclusion_preprod", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Structure removed successfully.
        /// </summary>
        public static string structure_removed_successfully {
            get {
                return ResourceManager.GetString("structure_removed_successfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Selected Structure.
        /// </summary>
        public static string structure_selected {
            get {
                return ResourceManager.GetString("structure_selected", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Structure Status Summary.
        /// </summary>
        public static string structure_status_recap {
            get {
                return ResourceManager.GetString("structure_status_recap", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Structures found.
        /// </summary>
        public static string structures_found {
            get {
                return ResourceManager.GetString("structures_found", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Management of relationships between structures and partners.
        /// </summary>
        public static string structures_partners_management {
            get {
                return ResourceManager.GetString("structures_partners_management", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Structures &amp; Partners.
        /// </summary>
        public static string structures_partners_title {
            get {
                return ResourceManager.GetString("structures_partners_title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Roles have been successfully assigned..
        /// </summary>
        public static string success_roles_assigned_message {
            get {
                return ResourceManager.GetString("success_roles_assigned_message", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Roles assigned successfully.
        /// </summary>
        public static string success_roles_assigned_title {
            get {
                return ResourceManager.GetString("success_roles_assigned_title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Maximum upload size allowed.
        /// </summary>
        public static string supportingdocuments_comment_sizeattachments_xml {
            get {
                return ResourceManager.GetString("supportingdocuments_comment_sizeattachments_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Active coupons.
        /// </summary>
        public static string tab_active_coupons {
            get {
                return ResourceManager.GetString("tab_active_coupons", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à All coupons.
        /// </summary>
        public static string tab_all_coupons {
            get {
                return ResourceManager.GetString("tab_all_coupons", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Expired coupons.
        /// </summary>
        public static string tab_expired_coupons {
            get {
                return ResourceManager.GetString("tab_expired_coupons", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à No coupons found for this profile..
        /// </summary>
        public static string tab_no_coupons {
            get {
                return ResourceManager.GetString("tab_no_coupons", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Used coupons.
        /// </summary>
        public static string tab_used_coupons {
            get {
                return ResourceManager.GetString("tab_used_coupons", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Actions.
        /// </summary>
        public static string table_header_actions {
            get {
                return ResourceManager.GetString("table_header_actions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Coupon Status.
        /// </summary>
        public static string table_header_coupon_status {
            get {
                return ResourceManager.GetString("table_header_coupon_status", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Create Date .
        /// </summary>
        public static string table_header_creation_date {
            get {
                return ResourceManager.GetString("table_header_creation_date", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à FirstName.
        /// </summary>
        public static string table_header_first_name {
            get {
                return ResourceManager.GetString("table_header_first_name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à ID.
        /// </summary>
        public static string table_header_id {
            get {
                return ResourceManager.GetString("table_header_id", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Label.
        /// </summary>
        public static string table_header_label {
            get {
                return ResourceManager.GetString("table_header_label", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Last Name.
        /// </summary>
        public static string table_header_last_name {
            get {
                return ResourceManager.GetString("table_header_last_name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Ablaufzeit des Warenkorbs (für den Benutzer in Indiv sichtbar).
        /// </summary>
        public static string Temps_Expiration_Panier {
            get {
                return ResourceManager.GetString("Temps_Expiration_Panier", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Error saving cart time: {0}.
        /// </summary>
        public static string temps_panier_save_error {
            get {
                return ResourceManager.GetString("temps_panier_save_error", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Cart time saved successfully.
        /// </summary>
        public static string temps_panier_saved_successfully {
            get {
                return ResourceManager.GetString("temps_panier_saved_successfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à The file will be deleted in: {0} minute(s) and {1} second(s). Do you want to proceed?.
        /// </summary>
        public static string time_warning_message {
            get {
                return ResourceManager.GetString("time_warning_message", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Time Warning.
        /// </summary>
        public static string time_warning_title {
            get {
                return ResourceManager.GetString("time_warning_title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Edit a section.
        /// </summary>
        public static string title_edit_section {
            get {
                return ResourceManager.GetString("title_edit_section", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Parameters.
        /// </summary>
        public static string title_parameters {
            get {
                return ResourceManager.GetString("title_parameters", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Missing keys added successfully.
        /// </summary>
        public static string toast_add_missing_keys_success {
            get {
                return ResourceManager.GetString("toast_add_missing_keys_success", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Adjustment made.
        /// </summary>
        public static string toast_adjustment_done {
            get {
                return ResourceManager.GetString("toast.adjustment_done", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Copy DEV to PROD successful.
        /// </summary>
        public static string toast_copy_to_prod_success {
            get {
                return ResourceManager.GetString("toast_copy_to_prod_success", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Copy DEV to TEST successful.
        /// </summary>
        public static string toast_copy_to_test_success {
            get {
                return ResourceManager.GetString("toast_copy_to_test_success", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à The number of coupons has been reduced to 2000..
        /// </summary>
        public static string toast_coupons_reduced_to_2000 {
            get {
                return ResourceManager.GetString("toast.coupons_reduced_to_2000", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à The number of coupons has been reduced to {0}..
        /// </summary>
        public static string toast_coupons_reduced_to_x {
            get {
                return ResourceManager.GetString("toast.coupons_reduced_to_x", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Connection failed. Please select another structure..
        /// </summary>
        public static string toast_message_connection_error_body {
            get {
                return ResourceManager.GetString("toast_message_connection_error_body", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Connection Error.
        /// </summary>
        public static string toast_message_connection_error_title {
            get {
                return ResourceManager.GetString("toast_message_connection_error_title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Data loaded successfully.
        /// </summary>
        public static string toast_message_data_loaded_success {
            get {
                return ResourceManager.GetString("toast_message_data_loaded_success", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Failed to delete structure.
        /// </summary>
        public static string toast_message_delete_structure_failed {
            get {
                return ResourceManager.GetString("toast_message_delete_structure_failed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à The structure was successfully deleted..
        /// </summary>
        public static string toast_message_delete_success {
            get {
                return ResourceManager.GetString("toast_message_delete_success", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à The deletion was successfully completed..
        /// </summary>
        public static string toast_message_deletion_successful {
            get {
                return ResourceManager.GetString("toast_message_deletion_successful", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à There are still empty required fields.
        /// </summary>
        public static string toast_message_empty_required_fields {
            get {
                return ResourceManager.GetString("toast_message_empty_required_fields", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à The temporary file has been deleted because the time is up..
        /// </summary>
        public static string toast_message_file_deletion {
            get {
                return ResourceManager.GetString("toast_message_file_deletion", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à  Form submission failed: {0}.
        /// </summary>
        public static string toast_message_form_submission_failed {
            get {
                return ResourceManager.GetString("toast_message_form_submission_failed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à he form contains validation errors.
        /// </summary>
        public static string toast_message_form_validation_failed {
            get {
                return ResourceManager.GetString("toast_message_form_validation_failed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à No changes in this form!.
        /// </summary>
        public static string toast_message_no_changes_in_form {
            get {
                return ResourceManager.GetString("toast_message_no_changes_in_form", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Failed to load structure data.
        /// </summary>
        public static string toast_message_structure_failed {
            get {
                return ResourceManager.GetString("toast_message_structure_failed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à The partner&apos;s modifications have been successfully saved..
        /// </summary>
        public static string toast_message_success_edit_partner_saved {
            get {
                return ResourceManager.GetString("toast_message_success_edit_partner_saved", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Structure Deletion Error.
        /// </summary>
        public static string toast_message_title_delete_structure_error {
            get {
                return ResourceManager.GetString("toast_message_title_delete_structure_error", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Structure Deletion Successful.
        /// </summary>
        public static string toast_message_title_delete_structure_success {
            get {
                return ResourceManager.GetString("toast_message_title_delete_structure_success", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Deletion Successful.
        /// </summary>
        public static string toast_message_title_deletion_success {
            get {
                return ResourceManager.GetString("toast_message_title_deletion_success", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Erreur.
        /// </summary>
        public static string toast_message_title_error {
            get {
                return ResourceManager.GetString("toast_message_title_error", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à File Deletion.
        /// </summary>
        public static string toast_message_title_file_deletion {
            get {
                return ResourceManager.GetString("toast_message_title_file_deletion", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Form Validation.
        /// </summary>
        public static string toast_message_title_form_validation {
            get {
                return ResourceManager.GetString("toast_message_title_form_validation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Changes in Progress.
        /// </summary>
        public static string toast_message_title_modifications_in_progress {
            get {
                return ResourceManager.GetString("toast_message_title_modifications_in_progress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à  Success.
        /// </summary>
        public static string toast_message_title_success {
            get {
                return ResourceManager.GetString("toast_message_title_success", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Modification Successful.
        /// </summary>
        public static string toast_message_title_success_edit {
            get {
                return ResourceManager.GetString("toast_message_title_success_edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Number adjusted.
        /// </summary>
        public static string toast_number_adjusted {
            get {
                return ResourceManager.GetString("toast.number_adjusted", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Registration successful.
        /// </summary>
        public static string toast_save_success_message {
            get {
                return ResourceManager.GetString("toast_save_success_message", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Total partners.
        /// </summary>
        public static string total_partners {
            get {
                return ResourceManager.GetString("total_partners", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Transfer photo clock-in.
        /// </summary>
        public static string transfere_pointage_photo {
            get {
                return ResourceManager.GetString("transfere-pointage-photo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Generate custom XML file.
        /// </summary>
        public static string transfert_pointagephoto_bouton_generer_xml {
            get {
                return ResourceManager.GetString("transfert_pointagephoto_bouton_generer_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Back.
        /// </summary>
        public static string transfert_pointagephoto_bouton_retour {
            get {
                return ResourceManager.GetString("transfert_pointagephoto_bouton_retour", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à  Processing....
        /// </summary>
        public static string transfert_pointagephoto_bouton_traitement {
            get {
                return ResourceManager.GetString("transfert_pointagephoto_bouton_traitement", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à  Transfer.
        /// </summary>
        public static string transfert_pointagephoto_bouton_transferer {
            get {
                return ResourceManager.GetString("transfert_pointagephoto_bouton_transferer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Verify.
        /// </summary>
        public static string transfert_pointagephoto_bouton_verifier {
            get {
                return ResourceManager.GetString("transfert_pointagephoto_bouton_verifier", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Transfer failed. Make sure the source location has an image..
        /// </summary>
        public static string transfert_pointagephoto_echec_transfert {
            get {
                return ResourceManager.GetString("transfert_pointagephoto_echec_transfert", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à An error occurred while loading physical locations: {0}.
        /// </summary>
        public static string transfert_pointagephoto_erreur_chargement {
            get {
                return ResourceManager.GetString("transfert_pointagephoto_erreur_chargement", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Please select a target physical location..
        /// </summary>
        public static string transfert_pointagephoto_erreur_cible {
            get {
                return ResourceManager.GetString("transfert_pointagephoto_erreur_cible", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Please select a source physical location..
        /// </summary>
        public static string transfert_pointagephoto_erreur_source {
            get {
                return ResourceManager.GetString("transfert_pointagephoto_erreur_source", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à  An error occurred during transfer: {0}.
        /// </summary>
        public static string transfert_pointagephoto_exception_transfert {
            get {
                return ResourceManager.GetString("transfert_pointagephoto_exception_transfert", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Environment not defined..
        /// </summary>
        public static string transfert_pointagephoto_export_environnement_indefini {
            get {
                return ResourceManager.GetString("transfert_pointagephoto_export_environnement_indefini", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à  Environment not defined..
        /// </summary>
        public static string transfert_pointagephoto_export_salle_environnement_indefini {
            get {
                return ResourceManager.GetString("transfert_pointagephoto_export_salle_environnement_indefini", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Structure ID missing..
        /// </summary>
        public static string transfert_pointagephoto_export_salle_structure_id_manquant {
            get {
                return ResourceManager.GetString("transfert_pointagephoto_export_salle_structure_id_manquant", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Error generating XML file: {0}.
        /// </summary>
        public static string transfert_pointagephoto_export_salle_xml_erreur {
            get {
                return ResourceManager.GetString("transfert_pointagephoto_export_salle_xml_erreur", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à XML file successfully generated..
        /// </summary>
        public static string transfert_pointagephoto_export_salle_xml_succes {
            get {
                return ResourceManager.GetString("transfert_pointagephoto_export_salle_xml_succes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Structure ID missing..
        /// </summary>
        public static string transfert_pointagephoto_export_structure_id_manquant {
            get {
                return ResourceManager.GetString("transfert_pointagephoto_export_structure_id_manquant", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Error generating XML file: {0}.
        /// </summary>
        public static string transfert_pointagephoto_export_xml_erreur {
            get {
                return ResourceManager.GetString("transfert_pointagephoto_export_xml_erreur", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à XML file successfully generated..
        /// </summary>
        public static string transfert_pointagephoto_export_xml_succes {
            get {
                return ResourceManager.GetString("transfert_pointagephoto_export_xml_succes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Rank {0}, seat {1} — present in the target but not in the source.
        /// </summary>
        public static string transfert_pointagephoto_incoherence_cible {
            get {
                return ResourceManager.GetString("transfert_pointagephoto_incoherence_cible", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à  + {0} other items not displayed..
        /// </summary>
        public static string transfert_pointagephoto_incoherence_cible_autres {
            get {
                return ResourceManager.GetString("transfert_pointagephoto_incoherence_cible_autres", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Rank {0}, seat {1} — present in the source but not in the target.
        /// </summary>
        public static string transfert_pointagephoto_incoherence_source {
            get {
                return ResourceManager.GetString("transfert_pointagephoto_incoherence_source", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à + {0} other items not displayed..
        /// </summary>
        public static string transfert_pointagephoto_incoherence_source_autres {
            get {
                return ResourceManager.GetString("transfert_pointagephoto_incoherence_source_autres", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Loading....
        /// </summary>
        public static string transfert_pointagephoto_label_chargement {
            get {
                return ResourceManager.GetString("transfert_pointagephoto_label_chargement", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Target physical location:.
        /// </summary>
        public static string transfert_pointagephoto_label_cible {
            get {
                return ResourceManager.GetString("transfert_pointagephoto_label_cible", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Source physical location (with image):.
        /// </summary>
        public static string transfert_pointagephoto_label_source {
            get {
                return ResourceManager.GetString("transfert_pointagephoto_label_source", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à  -- Select a target physical location --.
        /// </summary>
        public static string transfert_pointagephoto_option_cible_defaut {
            get {
                return ResourceManager.GetString("transfert_pointagephoto_option_cible_defaut", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à -- Select a source physical location --.
        /// </summary>
        public static string transfert_pointagephoto_option_source_defaut {
            get {
                return ResourceManager.GetString("transfert_pointagephoto_option_source_defaut", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à  Data loaded successfully.
        /// </summary>
        public static string transfert_pointagephoto_succes_chargement {
            get {
                return ResourceManager.GetString("transfert_pointagephoto_succes_chargement", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Photo time tracking transfer was successful from physical location {0} to {1}..
        /// </summary>
        public static string transfert_pointagephoto_succes_transfert {
            get {
                return ResourceManager.GetString("transfert_pointagephoto_succes_transfert", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Photo tagging {0}{1}.
        /// </summary>
        public static string transfert_pointagephoto_titre_page {
            get {
                return ResourceManager.GetString("transfert_pointagephoto_titre_page", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Photo tagging transfer on (x,y).
        /// </summary>
        public static string transfert_pointagephoto_titre_section {
            get {
                return ResourceManager.GetString("transfert_pointagephoto_titre_section", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Fix the inconsistencies before transferring..
        /// </summary>
        public static string transfert_pointagephoto_transfert_incoherences_a_corriger {
            get {
                return ResourceManager.GetString("transfert_pointagephoto_transfert_incoherences_a_corriger", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Select a source and a target location..
        /// </summary>
        public static string transfert_pointagephoto_transfert_selection_lieux {
            get {
                return ResourceManager.GetString("transfert_pointagephoto_transfert_selection_lieux", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Physical locations are compatible..
        /// </summary>
        public static string transfert_pointagephoto_verification_compatibles {
            get {
                return ResourceManager.GetString("transfert_pointagephoto_verification_compatibles", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Error during verification.
        /// </summary>
        public static string transfert_pointagephoto_verification_erreur {
            get {
                return ResourceManager.GetString("transfert_pointagephoto_verification_erreur", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à  Inconsistencies have been detected..
        /// </summary>
        public static string transfert_pointagephoto_verification_incoherences_detectees {
            get {
                return ResourceManager.GetString("transfert_pointagephoto_verification_incoherences_detectees", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Select a source and a target location to verify..
        /// </summary>
        public static string transfert_pointagephoto_verification_selection_lieux {
            get {
                return ResourceManager.GetString("transfert_pointagephoto_verification_selection_lieux", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à present in the target but not in the source.
        /// </summary>
        public static string transfert_resultats_cible_unique {
            get {
                return ResourceManager.GetString("transfert_resultats_cible_unique", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Physical locations are identical..
        /// </summary>
        public static string transfert_resultats_identiques_vignette {
            get {
                return ResourceManager.GetString("transfert_resultats_identiques_vignette", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Inconsistencies detected:.
        /// </summary>
        public static string transfert_resultats_incoherences_titre {
            get {
                return ResourceManager.GetString("transfert_resultats_incoherences_titre", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à + {0} more items not displayed..
        /// </summary>
        public static string transfert_resultats_plus_elements {
            get {
                return ResourceManager.GetString("transfert_resultats_plus_elements", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à present in the source but not in the target.
        /// </summary>
        public static string transfert_resultats_source_unique {
            get {
                return ResourceManager.GetString("transfert_resultats_source_unique", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Generate badge XML file.
        /// </summary>
        public static string transfert_vignette_bouton_generer_xml {
            get {
                return ResourceManager.GetString("transfert_vignette_bouton_generer_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Processing....
        /// </summary>
        public static string transfert_vignette_bouton_traitement {
            get {
                return ResourceManager.GetString("transfert_vignette_bouton_traitement", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Transfer Thumbnails.
        /// </summary>
        public static string transfert_vignette_bouton_transferer {
            get {
                return ResourceManager.GetString("transfert_vignette_bouton_transferer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à  Verify.
        /// </summary>
        public static string transfert_vignette_bouton_verifier {
            get {
                return ResourceManager.GetString("transfert_vignette_bouton_verifier", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Target Physical Location.
        /// </summary>
        public static string transfert_vignette_label_cible {
            get {
                return ResourceManager.GetString("transfert_vignette_label_cible", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Source Physical Location.
        /// </summary>
        public static string transfert_vignette_label_source {
            get {
                return ResourceManager.GetString("transfert_vignette_label_source", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Select target physical location.
        /// </summary>
        public static string transfert_vignette_option_cible_defaut {
            get {
                return ResourceManager.GetString("transfert_vignette_option_cible_defaut", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Select source physical location.
        /// </summary>
        public static string transfert_vignette_option_source_defaut {
            get {
                return ResourceManager.GetString("transfert_vignette_option_source_defaut", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Thumbnail Transfer.
        /// </summary>
        public static string transfert_vignette_titre_section {
            get {
                return ResourceManager.GetString("transfert_vignette_titre_section", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Choose variables.
        /// </summary>
        public static string translation_choose_variables {
            get {
                return ResourceManager.GetString("translation_choose_variables", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Translation key.
        /// </summary>
        public static string translation_key {
            get {
                return ResourceManager.GetString("translation_key", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Fully Selected.
        /// </summary>
        public static string translation_variable_fully_selected {
            get {
                return ResourceManager.GetString("translation_variable_fully_selected", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Not Selected.
        /// </summary>
        public static string translation_variable_not_selected {
            get {
                return ResourceManager.GetString("translation_variable_not_selected", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Partially Selected.
        /// </summary>
        public static string translation_variable_partially_selected {
            get {
                return ResourceManager.GetString("translation_variable_partially_selected", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Translation Management.
        /// </summary>
        public static string translations_terms {
            get {
                return ResourceManager.GetString("translations-terms", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Translations.
        /// </summary>
        public static string translations_title {
            get {
                return ResourceManager.GetString("translations_title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Commercial management tools.
        /// </summary>
        public static string tst_tool_commerce_description {
            get {
                return ResourceManager.GetString("tst_tool_commerce_description", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à  Commercial management.
        /// </summary>
        public static string tst_tool_commerce_name {
            get {
                return ResourceManager.GetString("tst_tool_commerce_name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Settings and configuration management.
        /// </summary>
        public static string tst_tool_configuration_description {
            get {
                return ResourceManager.GetString("tst_tool_configuration_description", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Configuration.
        /// </summary>
        public static string tst_tool_configuration_name {
            get {
                return ResourceManager.GetString("tst_tool_configuration_name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Monitoring and tracking.
        /// </summary>
        public static string tst_tool_monitoring_description {
            get {
                return ResourceManager.GetString("tst_tool_monitoring_description", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Monitoring.
        /// </summary>
        public static string tst_tool_monitoring_name {
            get {
                return ResourceManager.GetString("tst_tool_monitoring_name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Partner management.
        /// </summary>
        public static string tst_tool_partners_description {
            get {
                return ResourceManager.GetString("tst_tool_partners_description", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à  Partner management.
        /// </summary>
        public static string tst_tool_partners_name {
            get {
                return ResourceManager.GetString("tst_tool_partners_name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Management of roles.
        /// </summary>
        public static string tst_tool_roles_description {
            get {
                return ResourceManager.GetString("tst_tool_roles_description", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Roles management.
        /// </summary>
        public static string tst_tool_roles_name {
            get {
                return ResourceManager.GetString("tst_tool_roles_name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Security management.
        /// </summary>
        public static string tst_tool_securite_description {
            get {
                return ResourceManager.GetString("tst_tool_securite_description", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Security.
        /// </summary>
        public static string tst_tool_securite_name {
            get {
                return ResourceManager.GetString("tst_tool_securite_name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Translation management.
        /// </summary>
        public static string tst_tool_traductions_description {
            get {
                return ResourceManager.GetString("tst_tool_traductions_description", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Translations.
        /// </summary>
        public static string tst_tool_traductions_name {
            get {
                return ResourceManager.GetString("tst_tool_traductions_name", resourceCulture);
            }
        }

        /// <summary>
        ///   Recherche une chaîne localisée semblable à Development and debugging tools.
        /// </summary>
        public static string tst_tool_outils_dev_description {
            get {
                return ResourceManager.GetString("tst_tool_outils_dev_description", resourceCulture);
            }
        }

        /// <summary>
        ///   Recherche une chaîne localisée semblable à Dev Tools.
        /// </summary>
        public static string tst_tool_outils_dev_name {
            get {
                return ResourceManager.GetString("tst_tool_outils_dev_name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Client.
        /// </summary>
        public static string unidy_comment_client_xml {
            get {
                return ResourceManager.GetString("unidy_comment_client_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Redirect URL.
        /// </summary>
        public static string unidy_comment_redirecturl_xml {
            get {
                return ResourceManager.GetString("unidy_comment_redirecturl_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à  Secret.
        /// </summary>
        public static string unidy_comment_secret_xml {
            get {
                return ResourceManager.GetString("unidy_comment_secret_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Token service URL.
        /// </summary>
        public static string unidy_comment_urltokenservice_xml {
            get {
                return ResourceManager.GetString("unidy_comment_urltokenservice_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à User info URL.
        /// </summary>
        public static string unidy_comment_urluinfo_xml {
            get {
                return ResourceManager.GetString("unidy_comment_urluinfo_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Used coupons only.
        /// </summary>
        public static string used_coupons_only {
            get {
                return ResourceManager.GetString("used_coupons_only", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Unspecified.
        /// </summary>
        public static string username_nom_specifie_webtracing {
            get {
                return ResourceManager.GetString("username_nom_specifie_webtracing", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Three cases for the amount value: 0:0, 1: free, 2: not included.
        /// </summary>
        public static string variables_comment_montant_gratuit_xml {
            get {
                return ResourceManager.GetString("variables_comment_montant_gratuit_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Alphanumeric.
        /// </summary>
        public static string variables_comment_type_alpha_xml {
            get {
                return ResourceManager.GetString("variables_comment_type_alpha_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Values ranging from 5 to 7.
        /// </summary>
        public static string variables_comment_xml {
            get {
                return ResourceManager.GetString("variables_comment_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à  Hours limitation.
        /// </summary>
        public static string virement_comment_hourslimitation_xml {
            get {
                return ResourceManager.GetString("virement_comment_hourslimitation_xml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Visible only for Admin status.
        /// </summary>
        public static string visible_only_for_admin_status {
            get {
                return ResourceManager.GetString("visible_only_for_admin_status", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à View empty fields for the language.
        /// </summary>
        public static string voir_champs_vides_langue {
            get {
                return ResourceManager.GetString("voir_champs_vides_langue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à To be deleted.
        /// </summary>
        public static string webtracing_button_delete {
            get {
                return ResourceManager.GetString("webtracing_button_delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Deleted.
        /// </summary>
        public static string webtracing_button_deleted {
            get {
                return ResourceManager.GetString("webtracing_button_deleted", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Actions.
        /// </summary>
        public static string webtracing_column_actions {
            get {
                return ResourceManager.GetString("webtracing_column_actions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Database.
        /// </summary>
        public static string webtracing_column_database {
            get {
                return ResourceManager.GetString("webtracing_column_database", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Deleted.
        /// </summary>
        public static string webtracing_column_deleted {
            get {
                return ResourceManager.GetString("webtracing_column_deleted", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Deleted By.
        /// </summary>
        public static string webtracing_column_deleted_by {
            get {
                return ResourceManager.GetString("webtracing_column_deleted_by", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Deletion Date.
        /// </summary>
        public static string webtracing_column_deletion_date {
            get {
                return ResourceManager.GetString("webtracing_column_deletion_date", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Name.
        /// </summary>
        public static string webtracing_column_name {
            get {
                return ResourceManager.GetString("webtracing_column_name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Start Date .
        /// </summary>
        public static string webtracing_column_start_date {
            get {
                return ResourceManager.GetString("webtracing_column_start_date", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à End Date.
        /// </summary>
        public static string webtracing_column_stop_date {
            get {
                return ResourceManager.GetString("webtracing_column_stop_date", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Deleted.
        /// </summary>
        public static string webtracing_deleted {
            get {
                return ResourceManager.GetString("webtracing_deleted", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à List of Structures with WebTracing Connections.
        /// </summary>
        public static string webtracing_list_title {
            get {
                return ResourceManager.GetString("webtracing_list_title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à No structure or connection found..
        /// </summary>
        public static string webtracing_no_structure_found {
            get {
                return ResourceManager.GetString("webtracing_no_structure_found", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à No.
        /// </summary>
        public static string webtracing_not_deleted {
            get {
                return ResourceManager.GetString("webtracing_not_deleted", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Unspecified.
        /// </summary>
        public static string webtracing_unspecified {
            get {
                return ResourceManager.GetString("webtracing_unspecified", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Catalogs of Offers.
        /// </summary>
        public static string widget_catalogue_offre {
            get {
                return ResourceManager.GetString("widget-catalogue-offre", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Cross-selling.
        /// </summary>
        public static string widget_cross_selling {
            get {
                return ResourceManager.GetString("widget-cross-selling", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Queue config..
        /// </summary>
        public static string widget_waiting_list {
            get {
                return ResourceManager.GetString("widget-waiting-list", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à With partners.
        /// </summary>
        public static string with_partners {
            get {
                return ResourceManager.GetString("with_partners", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à The environment is not defined in the configuration..
        /// </summary>
        public static string xml_generation_environment_undefined {
            get {
                return ResourceManager.GetString("xml_generation_environment_undefined", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Error while generating the XML file: {0}.
        /// </summary>
        public static string xml_generation_error {
            get {
                return ResourceManager.GetString("xml_generation_error", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à StructureId is null, generation is not possible..
        /// </summary>
        public static string xml_generation_structureid_null {
            get {
                return ResourceManager.GetString("xml_generation_structureid_null", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à XML file successfully generated..
        /// </summary>
        public static string xml_generation_success {
            get {
                return ResourceManager.GetString("xml_generation_success", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Recherche une chaîne localisée semblable à Error while loading XML files for service {0}: {1}.
        /// </summary>
        public static string xml_loading_error_for_service {
            get {
                return ResourceManager.GetString("xml_loading_error_for_service", resourceCulture);
            }
        }
    }
}
